import { app, InvocationContext, StorageQueueHand<PERSON> } from "@azure/functions";
import * as sql from "mssql";
import J<PERSON><PERSON><PERSON> from "jszip";
import { BlobServiceClient } from "@azure/storage-blob";

/**
 * Creates a new SQL connection with proper configuration
 */
async function createSqlConnection(context: InvocationContext): Promise<sql.ConnectionPool> {
  // Create a new connection pool with timeout and retry settings
  const pool = new sql.ConnectionPool(process.env.SQL_CONN_STR!);

  try {
    await pool.connect();
    context.log("SQL connection established successfully");
    return pool;
  } catch (error: any) {
    context.error("Failed to connect to SQL database", error);
    throw error;
  }
}

/**
 * Executes a database operation with retry logic
 */
async function executeWithRetry<T>(
  operation: () => Promise<T>,
  context: InvocationContext,
  maxRetries: number = 3
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      context.warn(`Database operation failed (attempt ${attempt}/${maxRetries}):`, error.message);

      if (attempt < maxRetries) {
        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

export const processImage: StorageQueueHandler = async (
  rawMessage: unknown,
  context: InvocationContext
): Promise<void> => {
  try {
    // Log at the very beginning to see if the function is being triggered
    console.log("processImage function triggered - console.log");
    context.log("processImage function triggered - context.log");

    // Log environment variables (without sensitive information)
    context.log("Environment variables check:");
    context.log(`AzureWebJobsStorage exists: ${!!process.env.AzureWebJobsStorage}`);
    context.log(`SQL_CONN_STR exists: ${!!process.env.SQL_CONN_STR}`);

    // Log the raw message
    try {
      context.log(`Raw message type: ${typeof rawMessage}`);
      context.log(`Raw message: ${JSON.stringify(rawMessage)}`);
    } catch (e: any) {
      context.log(`Error stringifying raw message: ${e.message}`);
      context.log(`Raw message toString: ${String(rawMessage)}`);
    }

    // 1) Parse the message
    let messageData: { submissionId: number; imageName: string; zipBlobName: string };

    // Check if rawMessage is already an object with the expected properties
    if (typeof rawMessage === 'object' && rawMessage !== null &&
        'submissionId' in rawMessage && 'imageName' in rawMessage && 'zipBlobName' in rawMessage) {
      // It's already the right format, use it directly
      messageData = rawMessage as { submissionId: number; imageName: string; zipBlobName: string };
      context.log("Message is already in the correct format");
    } else {
      // Try to parse it from a string
      const msg = String(rawMessage);
      context.log(`Raw message as string: ${msg}`);

      try {
        // Try direct JSON parse first
        messageData = JSON.parse(msg);
        context.log("Message parsed as JSON string");
      } catch (parseError: any) {
        context.error("Failed to parse message as JSON", {
          error: parseError.message,
          rawMessage: msg
        });

        // As a last resort, try base64 decoding
        try {
          const decodedMsg = Buffer.from(msg, "base64").toString("utf8");
          context.log(`Decoded message: ${decodedMsg}`);
          messageData = JSON.parse(decodedMsg);
          context.log("Message parsed after base64 decoding");
        } catch (decodeError: any) {
          context.error("Failed to decode and parse message", {
            error: decodeError.message,
            rawMessage: msg
          });
          throw new Error(`Invalid message format: ${decodeError.message}`);
        }
      }
    }

    const { submissionId, imageName, zipBlobName } = messageData;
    context.log(`Processing image: ${imageName} for submission ${submissionId}`);

    // 2) Download the ZIP file from blob storage
    const blobSvc = BlobServiceClient.fromConnectionString(process.env.AzureWebJobsStorage!);
    const zipContainer = blobSvc.getContainerClient("zip-submissions");
    const zipBlobClient = zipContainer.getBlockBlobClient(zipBlobName);

    // Check if blob exists
    const exists = await zipBlobClient.exists();
    if (!exists) {
      throw new Error(`ZIP blob ${zipBlobName} not found`);
    }

    // Download the blob with memory optimization
    const downloadResponse = await zipBlobClient.download(0);
    const chunks: Buffer[] = [];
    let totalSize = 0;
    const maxZipSize = 50 * 1024 * 1024; // 50MB limit

    // Read the stream with size checking
    for await (const chunk of downloadResponse.readableStreamBody!) {
      const buffer = Buffer.from(chunk);
      totalSize += buffer.length;

      if (totalSize > maxZipSize) {
        // Clear chunks to free memory before throwing
        chunks.length = 0;
        throw new Error(`ZIP file too large: ${totalSize} bytes (max: ${maxZipSize})`);
      }

      chunks.push(buffer);
    }

    // Combine chunks into a single buffer with error handling
    let zipBuffer: Buffer;
    try {
      zipBuffer = Buffer.concat(chunks);
      context.log(`Downloaded ZIP file (${zipBuffer.length} bytes)`);
    } catch (error: any) {
      // Clear chunks to free memory
      chunks.length = 0;
      throw new Error(`Failed to concatenate ZIP buffer: ${error.message}`);
    }

    // Clear chunks array to free memory immediately
    chunks.length = 0;

    // 3) Extract the specific image from the ZIP
    let zip: JSZip;
    let imageData: Buffer;

    try {
      zip = await JSZip.loadAsync(zipBuffer);

      // Clear zipBuffer to free memory
      zipBuffer = Buffer.alloc(0);

      // Find the specific image entry
      const imageEntry = zip.files[imageName];
      if (!imageEntry) {
        throw new Error(`Image ${imageName} not found in ZIP`);
      }

      // Extract the image data
      imageData = await imageEntry.async("nodebuffer");
      context.log(`Extracted image: ${imageName} (${imageData.length} bytes)`);

    } catch (e) {
      // Clear zipBuffer to free memory on error
      zipBuffer = Buffer.alloc(0);
      if (e instanceof Error && e.message.includes("not found in ZIP")) {
        throw e;
      }
      context.error("ZIP parse error", e);
      throw new Error("Invalid ZIP file");
    }

    // 4) Upload the image to blob storage
    const submissionsContainer = blobSvc.getContainerClient("submissions");
    await submissionsContainer.createIfNotExists();

    const imageBlobClient = submissionsContainer.getBlockBlobClient(`${submissionId}/${imageName}`);

    // Determine content type based on file extension
    let contentType = 'application/octet-stream';
    if (imageName.toLowerCase().endsWith('.jpg') || imageName.toLowerCase().endsWith('.jpeg')) {
      contentType = 'image/jpeg';
    } else if (imageName.toLowerCase().endsWith('.png')) {
      contentType = 'image/png';
    } else if (imageName.toLowerCase().endsWith('.gif')) {
      contentType = 'image/gif';
    } else if (imageName.toLowerCase().endsWith('.pdf')) {
      contentType = 'application/pdf';
    }

    // Upload the image
    await imageBlobClient.uploadData(imageData, {
      blobHTTPHeaders: { blobContentType: contentType },
      metadata: { submissionId: String(submissionId) }
    });

    const imageUrl = imageBlobClient.url;
    context.log(`Uploaded image to: ${imageUrl}`);

    // 5) Update the database with the image URL
    await executeWithRetry(async () => {
      let pool: sql.ConnectionPool | null = null;

      try {
        pool = await createSqlConnection(context);

        // First, get the current AttachmentUrls value
        const result = await pool.request()
          .input('id', sql.Int, submissionId)
          .query`SELECT AttachmentUrls FROM dbo.FastFieldSubmissions WHERE Id = @id`;

        // Parse existing URLs or create new object
        let urlMap: Record<string, string> = {};
        if (result.recordset[0]?.AttachmentUrls) {
          try {
            urlMap = JSON.parse(result.recordset[0].AttachmentUrls);
          } catch (e) {
            context.warn("Failed to parse existing AttachmentUrls, creating new object", e);
          }
        }

        // Add the new URL
        urlMap[imageName] = imageUrl;

        // Update the database
        await pool.request()
          .input('urls', sql.NVarChar(sql.MAX), JSON.stringify(urlMap))
          .input('id', sql.Int, submissionId)
          .query`UPDATE dbo.FastFieldSubmissions
                 SET AttachmentUrls = @urls
                 WHERE Id = @id`;

        context.log(`Updated database with URL for ${imageName}`);
      } finally {
        // Always close the connection
        if (pool) {
          try {
            await pool.close();
          } catch (closeError) {
            context.warn("Error closing SQL connection:", closeError);
          }
        }
      }
    }, context);

    context.log(`Image processing complete for ${imageName}`);
  } catch (error: any) {
    context.error("Function failed", {
      error: error.message,
      stack: error.stack
    });
    throw error; // Rethrow to trigger retry
  }
};

app.storageQueue("processImage", {
  queueName: "image-processing",
  connection: "AzureWebJobsStorage",
  handler: processImage,
});
