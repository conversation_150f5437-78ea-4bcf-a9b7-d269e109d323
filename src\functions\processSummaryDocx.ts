import { app, InvocationContext, Storage<PERSON>ueueHandler } from "@azure/functions";
import * as sql from "mssql";
import { Document, Packer, Paragraph, TextRun, HeadingLevel, PageBreak, AlignmentType, Table, TableRow, TableCell, WidthType, ImageRun, Bookmark, InternalHyperlink, Header, Footer, PageNumber, ExternalHyperlink } from "docx";
import { BlobServiceClient } from "@azure/storage-blob";
import { ClientSecretCredential } from "@azure/identity";
import { Client } from "@microsoft/microsoft-graph-client";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import sizeOf from "image-size";

// Load solution links data
const solutionLinksPath = path.join(__dirname, '../../solution-links.json');
const solutionLinksData = JSON.parse(fs.readFileSync(solutionLinksPath, 'utf8'));

// Load form definition for field mapping
const formDefPath = path.join(__dirname, '../../form-def.json');
const formDefinition = JSON.parse(fs.readFileSync(formDefPath, 'utf8'));

/**
 * Creates a mapping from field keys to question text from the form definition
 */
function createFieldToQuestionMap(formDef: any): Record<string, string> {
  const fieldMap: Record<string, string> = {};

  function extractFields(obj: any) {
    if (Array.isArray(obj)) {
      obj.forEach(item => extractFields(item));
    } else if (obj && typeof obj === 'object') {
      if (obj.fieldKey && obj.fieldName) {
        fieldMap[obj.fieldKey] = obj.fieldName;
      }
      Object.values(obj).forEach(value => extractFields(value));
    }
  }

  extractFields(formDef);
  return fieldMap;
}

// Create the field mapping
const fieldToQuestionMap = createFieldToQuestionMap(formDefinition);

// Define interfaces for better type safety
interface FormPayload {
  formId?: number;
  formName?: string;
  submissionId?: string;
  accountId?: number;
  main_form_facility_name?: string;
  main_form_first_name?: string;
  main_form_last_name?: string;
  main_form_email_address?: string;
  main_form_phone_number?: string;
  main_form_date?: string;
  main_form_nameplate?: string;
  formMetaData?: any;
  section_2?: MachineSection[];
  [key: string]: any;
}

interface MachineSection {
  sectionCounter: number;
  appended?: string;
  fields: {
    main_form_machine_title?: string;
    main_form_machine_type?: string[];
    main_form_general_machine_subform?: MachineSubform[];
    [key: string]: any;
  };
}

interface MachineSubform {
  reserved_instanceGuid?: string;
  general_machine_type?: string[];
  assessment_level?: string[];
  machine_name_and_id?: string;
  location_of_machine?: string;
  department?: string;
  machine_manufacturer?: string;
  machine_model_number?: string;
  machine_serial_number?: string;
  operator_names?: string;
  machine_description?: string;
  is_machine_in_operation?: number;
  was_machine_observed_during_observation?: number;
  known_injuries_associated_with_machine?: number;
  photo_of_machine_nameplate?: PhotoWithComment[];
  photo_of_machine_id_num?: PhotoWithComment[];
  photo_of_machine_front?: PhotoWithComment[];
  photo_of_machine_right?: PhotoWithComment[];
  photo_of_machine_back?: PhotoWithComment[];
  photo_of_machine_left?: PhotoWithComment[];
  section_40?: HazardSection[];
  [key: string]: any;
}

interface PhotoWithComment {
  comment: string;
  photo: string;
}

interface HazardSection {
  sectionCounter: number;
  appended?: string;
  fields: {
    field_403?: string; // Hazard name
    field_404?: PhotoWithComment[]; // Hazard photo
    field_405?: string; // Hazard description
    field_406?: string[]; // Recommended solutions
    multiline_5?: string; // Additional notes
    [key: string]: any;
  };
}

interface SubmissionData {
  id: number;
  payload: FormPayload;
  attachmentUrls?: Record<string, string>;
  zipBlobName?: string;
  submissionDate?: Date;
}

interface SolutionLink {
  name: string;
  url: string;
}

interface SolutionData {
  name: string;
  links: SolutionLink[];
}

interface SolutionLinksData {
  [key: string]: SolutionData;
}

/**
 * Helper function to format solutions with links
 */
function formatSolutionWithLinks(solutionKey: string, customText?: string): any[] {
  // Handle "other" option with custom text
  if (solutionKey === 'other' && customText) {
    return [
      new TextRun({
        text: `• ${customText}`,
        size: 22, // 11pt
      })
    ];
  }

  // Get solution data from the JSON
  const solutionData = (solutionLinksData as SolutionLinksData)[solutionKey];

  if (!solutionData) {
    // If solution not found in JSON, just display the key as text
    return [
      new TextRun({
        text: `• ${solutionKey}`,
        size: 22, // 11pt
      })
    ];
  }

  const children: any[] = [
    new TextRun({
      text: "• ",
      size: 22, // 11pt
    })
  ];

  // If no links, just show the solution name
  if (!solutionData.links || solutionData.links.length === 0) {
    children.push(
      new TextRun({
        text: solutionData.name,
        size: 22, // 11pt
      })
    );
    return children;
  }

  // If only one link, show solution name as hyperlink
  if (solutionData.links.length === 1) {
    children.push(
      new ExternalHyperlink({
        children: [
          new TextRun({
            text: solutionData.name,
            style: "Hyperlink",
            size: 22, // 11pt
          })
        ],
        link: solutionData.links[0].url,
      })
    );
    return children;
  }

  // If multiple links, show solution name followed by linked options
  children.push(
    new TextRun({
      text: `${solutionData.name}: `,
      size: 22, // 11pt
    })
  );

  // Add each link separated by commas
  solutionData.links.forEach((link, index) => {
    if (index > 0) {
      children.push(
        new TextRun({
          text: ", ",
          size: 22, // 11pt
        })
      );
    }
    children.push(
      new ExternalHyperlink({
        children: [
          new TextRun({
            text: link.name,
            style: "Hyperlink",
            size: 22, // 11pt
          })
        ],
        link: link.url,
      })
    );
  });

  return children;
}

/**
 * Helper function to format date for folder names
 */
function formatDateForFolder(dateString: string | undefined): string {
  if (!dateString) {
    return new Date().toISOString().split('T')[0]; // Default to today's date
  }

  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  } catch {
    return new Date().toISOString().split('T')[0]; // Fallback to today's date
  }
}

/**
 * Helper function to format date for filenames (M-D-YYYY format)
 */
function formatDateForFilename(dateString: string | undefined): string {
  if (!dateString) {
    const today = new Date();
    return `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
  }

  try {
    const date = new Date(dateString);
    return `${date.getMonth() + 1}-${date.getDate()}-${date.getFullYear()}`;
  } catch {
    const today = new Date();
    return `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
  }
}

/**
 * Helper function to sanitize folder names
 */
function sanitizeFolderName(name: string): string {
  // Remove or replace characters that are not allowed in SharePoint folder names
  return name
    .replace(/[<>:"/\\|?*']/g, '_') // Replace invalid characters including apostrophes with underscore
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[._]+$/, '') // Remove trailing dots and underscores
    .trim()
    .substring(0, 100); // Limit length to 100 characters
}

/**
 * Uploads DOCX file to SharePoint with proper folder structure
 */
async function uploadDocxToSharePoint(
  submission: SubmissionData,
  buffer: Buffer,
  graph: any,
  context: InvocationContext
): Promise<void> {
  const siteId = process.env.SHAREPOINT_SITE_ID!;
  let driveId = process.env.SHAREPOINT_DRIVE_ID;

  context.log(`Environment check - SHAREPOINT_SITE_ID: ${siteId ? 'SET' : 'NOT SET'}`);
  context.log(`Environment check - SHAREPOINT_DRIVE_ID: ${driveId ? 'SET' : 'NOT SET'}`);

  if (!siteId) {
    throw new Error("SHAREPOINT_SITE_ID environment variable is not set");
  }

  // If driveId is not set, get the default drive ID
  if (!driveId) {
    context.log("SHAREPOINT_DRIVE_ID not set, getting default drive ID");
    try {
      const driveResponse = await graph
        .api(`/sites/${siteId}/drive`)
        .get();
      driveId = driveResponse.id;
      context.log(`Retrieved default drive ID: ${driveId}`);
    } catch (error: any) {
      context.error(`Failed to get default drive ID: ${error.message}`);
      throw new Error(`Cannot access SharePoint drive: ${error.message}`);
    }
  } else {
    context.log(`Using configured drive ID: ${driveId}`);
  }

  // Create folder structure: Facility Name / Assessment Date
  const facilityName = sanitizeFolderName(submission.payload.main_form_facility_name || "Unknown Facility");
  const assessmentDate = formatDateForFolder(submission.payload.main_form_date);

  context.log(`Creating folder structure: Summaries/${facilityName}/${assessmentDate}`);
  context.log(`Raw facility name from payload: "${submission.payload.main_form_facility_name}"`);
  context.log(`Sanitized facility name: "${facilityName}"`);
  context.log(`Assessment date: "${assessmentDate}"`);

  // First, ensure the Summaries folder exists and get its ID
  let summariesFolderId: string;
  try {
    context.log("Getting Summaries folder ID");
    const summariesFolderResponse = await graph
      .api(`/sites/${siteId}/drives/${driveId}/root:/Summaries`)
      .get();
    summariesFolderId = summariesFolderResponse.id;
    context.log(`Found Summaries folder (ID: ${summariesFolderId})`);
  } catch (error: any) {
    context.error(`Summaries folder not found: ${error.message}`);
    context.error(`Summaries folder error details:`, {
      code: error.code,
      statusCode: error.statusCode,
      message: error.message,
      siteId: siteId,
      driveId: driveId
    });
    throw new Error(`Summaries folder not accessible: ${error.message}`);
  }

  // Create or get facility folder inside Summaries
  let facilityFolderId: string;
  try {
    // Try to get existing facility folder by listing all children and finding the match
    context.log(`Checking for existing facility folder: "${facilityName}" in Summaries`);
    const facilityFolderResponse = await graph
      .api(`/sites/${siteId}/drives/${driveId}/items/${summariesFolderId}/children`)
      .get();

    // Look for existing folder with matching name
    const existingFolder = facilityFolderResponse.value?.find((item: any) =>
      item.folder && item.name === facilityName
    );

    if (existingFolder) {
      facilityFolderId = existingFolder.id;
      context.log(`Found existing facility folder: "${facilityName}" (ID: ${facilityFolderId})`);
    } else {
      context.log(`No existing facility folder found with name: "${facilityName}"`);
      throw new Error("Facility folder not found");
    }
  } catch (error: any) {
    context.log(`Facility folder not found, creating new one. Error: ${error.message}`);
    try {
      // Create facility folder if it doesn't exist
      context.log(`Creating new facility folder: "${facilityName}" in Summaries folder ${summariesFolderId}`);
      const facilityFolderResponse = await graph
        .api(`/sites/${siteId}/drives/${driveId}/items/${summariesFolderId}/children`)
        .post({
          name: facilityName,
          folder: {},
          "@microsoft.graph.conflictBehavior": "rename"
        });
      facilityFolderId = facilityFolderResponse.id;
      context.log(`Successfully created new facility folder: "${facilityName}" (ID: ${facilityFolderId})`);
    } catch (createError: any) {
      context.error(`Failed to create facility folder: ${createError.message}`);
      context.error(`Create error details:`, {
        code: createError.code,
        statusCode: createError.statusCode,
        message: createError.message,
        requestId: createError.requestId,
        facilityName: facilityName,
        summariesFolderId: summariesFolderId,
        requestBody: {
          name: facilityName,
          folder: {},
          "@microsoft.graph.conflictBehavior": "rename"
        }
      });
      context.error(`Full error object:`, createError);
      throw new Error(`Folder creation failed: ${createError.message}`);
    }
  }

  // Create or get assessment date folder
  let dateFolderId: string;
  try {
    // Try to get existing date folder
    context.log(`Checking for existing date folder: ${assessmentDate} in facility folder ${facilityFolderId}`);
    const dateFolderResponse = await graph
      .api(`/sites/${siteId}/drives/${driveId}/items/${facilityFolderId}/children`)
      .filter(`name eq '${assessmentDate}'`)
      .get();

    if (dateFolderResponse.value && dateFolderResponse.value.length > 0) {
      dateFolderId = dateFolderResponse.value[0].id;
      context.log(`Found existing date folder: ${assessmentDate} (ID: ${dateFolderId})`);
    } else {
      throw new Error("Date folder not found in response");
    }
  } catch (error: any) {
    context.log(`Date folder not found, creating new one. Error: ${error.message}`);
    try {
      // Create date folder if it doesn't exist
      const dateFolderResponse = await graph
        .api(`/sites/${siteId}/drives/${driveId}/items/${facilityFolderId}/children`)
        .post({
          name: assessmentDate,
          folder: {},
          "@microsoft.graph.conflictBehavior": "rename"
        });
      dateFolderId = dateFolderResponse.id;
      context.log(`Created new date folder: ${assessmentDate} (ID: ${dateFolderId})`);
    } catch (createError: any) {
      context.error(`Failed to create date folder: ${createError.message}`);
      // Fallback: use facility folder directly
      context.log("Falling back to facility folder for file upload");
      dateFolderId = facilityFolderId;
    }
  }

  // Generate filename with clean format
  const formattedDate = formatDateForFilename(submission.payload.main_form_date);
  const filename = `Machine Assessment ${submission.id} ${formattedDate}.docx`;

  context.log(`Uploading file: ${filename} to folder ID: ${dateFolderId}`);

  try {
    // Upload DOCX file to the date folder
    const uploadResponse = await graph
      .api(`/sites/${siteId}/drives/${driveId}/items/${dateFolderId}:/${filename}:/content`)
      .header("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
      .put(buffer);

    context.log(`File upload response received. ID: ${uploadResponse.id}`);

    // Get the item info for the uploaded file
    const fileInfo = await graph
      .api(`/sites/${siteId}/drives/${driveId}/items/${uploadResponse.id}`)
      .get();

    context.log(`DOCX file uploaded successfully: ${fileInfo.webUrl}`);

    // Update file permissions to allow editing
    if (fileInfo.id) {
      try {
        // Set the file to be editable
        await graph
          .api(`/sites/${siteId}/drives/${driveId}/items/${fileInfo.id}`)
          .patch({
            "@microsoft.graph.conflictBehavior": "replace",
            file: {
              mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            }
          });

        context.log("File permissions updated to allow editing");
      } catch (permError: any) {
        context.warn(`Could not update file permissions: ${permError.message}`);
      }
    }
  } catch (uploadError: any) {
    context.error(`File upload failed: ${uploadError.message}`);
    context.error(`Upload error details:`, {
      code: uploadError.code,
      statusCode: uploadError.statusCode,
      message: uploadError.message,
      requestId: uploadError.requestId
    });
    throw uploadError;
  }
}

export const processSummaryDocx: StorageQueueHandler = async (
  rawMessage: unknown,
  context: InvocationContext
): Promise<void> => {
  try {
    // 1) Decode the submission ID
    const msg = String(rawMessage);
    const submissionId = /^\d+$/.test(msg)
      ? parseInt(msg, 10)
      : parseInt(Buffer.from(msg, "base64").toString("utf8"), 10);
    context.log("Processing submission ID:", submissionId);

    // 2) Load the submission data from SQL
    let pool: sql.ConnectionPool;
    try {
      pool = await sql.connect(process.env.SQL_CONN_STR!);
      context.log("Connected to SQL database");
    } catch (error: any) {
      context.error("Failed to connect to SQL database", error);
      throw new Error(`SQL connection failed: ${error.message}`);
    }

    let submission: SubmissionData;
    try {
      const { recordset } = await pool.request()
        .input('id', sql.Int, submissionId)
        .query(`
          SELECT Id, Payload, AttachmentUrls, ZipBlobName, GETDATE() as SubmissionDate
          FROM dbo.FastFieldSubmissions
          WHERE Id = @id
        `);

      if (!recordset || recordset.length === 0) {
        throw new Error(`Submission with ID ${submissionId} not found`);
      }

      submission = {
        id: recordset[0].Id,
        payload: JSON.parse(recordset[0].Payload),
        attachmentUrls: recordset[0].AttachmentUrls ? JSON.parse(recordset[0].AttachmentUrls) : {},
        zipBlobName: recordset[0].ZipBlobName,
        submissionDate: recordset[0].SubmissionDate
      };

      context.log(`Loaded submission data for ID ${submissionId}`);
    } catch (error: any) {
      context.error("Failed to load submission data", error);
      throw new Error(`Failed to load submission data: ${error.message}`);
    }

    // 3) Download images from blob storage
    context.log("Downloading images from blob storage for embedding in document");
    context.log(`Submission ID: ${submissionId}`);

    const imageMap: Record<string, string> = {};
    const tempDir = await createTempDir(context);

    try {
      await downloadImagesForSubmission(submission, tempDir, imageMap, context);
    } catch (error: any) {
      context.error("Error downloading images", error);
      // Continue with document generation even if image download fails
    }

    // 4) Create a professional document using docx library
    const doc = await createProfessionalDocument(
      submission,
      imageMap,
      context
    );

    // 5) Generate the document buffer
    const buffer = await Packer.toBuffer(doc);

    // 6) Authenticate to Microsoft Graph and upload
    const credential = new ClientSecretCredential(
      process.env.AZURE_TENANT_ID!,
      process.env.AZURE_CLIENT_ID!,
      process.env.AZURE_CLIENT_SECRET!
    );
    const graph = Client.initWithMiddleware({
      authProvider: {
        getAccessToken: async () =>
          (await credential.getToken(".default"))?.token || "",
      },
    });

    // 7) Upload to SharePoint with folder structure (with fallback)
    try {
      await uploadDocxToSharePoint(submission, buffer, graph, context);
    } catch (uploadError: any) {
      context.warn(`Folder structure upload failed: ${uploadError.message}`);
      context.log("Attempting fallback to flat structure");

      try {
        // Fallback to old flat structure
        const siteId = process.env.SHAREPOINT_SITE_ID!;
        const formattedDate = formatDateForFilename(submission.payload.main_form_date);
        const filename = `Machine Assessment ${submission.id} ${formattedDate}.docx`;
        const uploadPath = `Summaries/${filename}`;

        await graph
          .api(`/sites/${siteId}/drive/root:/${uploadPath}:/content`)
          .header("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
          .put(buffer);

        context.log(`✔️ Uploaded document using fallback structure: ${uploadPath}`);
      } catch (fallbackError: any) {
        context.error(`Both upload methods failed: ${fallbackError.message}`);
        throw fallbackError;
      }
    } finally {
      // Clean up temporary directory
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
        context.log(`Cleaned up temporary directory: ${tempDir}`);
      } catch (cleanupError: any) {
        context.warn(`Failed to clean up temporary directory: ${cleanupError.message}`);
      }
    }
  } catch (error: any) {
    context.error("Function failed", {
      error: error.message,
      stack: error.stack,
      functionName: "processSummaryDocx"
    });
    throw error; // Rethrow to trigger retry
  }
};

/**
 * Creates a temporary directory for storing images
 */
async function createTempDir(context: InvocationContext): Promise<string> {
  const tempDirName = `odiz-images-${Math.random().toString(36).substring(2, 9)}`;
  const tempDir = path.join(os.tmpdir(), tempDirName);

  try {
    fs.mkdirSync(tempDir, { recursive: true });
    context.log(`Created temporary directory: ${tempDir}`);
    return tempDir;
  } catch (error: any) {
    context.error(`Failed to create temporary directory: ${error.message}`);
    throw error;
  }
}

/**
 * Downloads images from blob storage for a submission
 */
async function downloadImagesForSubmission(
  submission: SubmissionData,
  tempDir: string,
  imageMap: Record<string, string>,
  context: InvocationContext
): Promise<void> {
  const { attachmentUrls = {} } = submission;

  context.log(`Attachment URLs count: ${Object.keys(attachmentUrls).length}`);

  if (Object.keys(attachmentUrls).length === 0) {
    context.log("No attachment URLs found");
    return;
  }

  // Connect to blob storage
  const blobServiceClient = BlobServiceClient.fromConnectionString(
    process.env.AzureWebJobsStorage!
  );

  // Process images in batches to avoid memory issues and timeouts
  const imageEntries = Object.entries(attachmentUrls);
  const batchSize = 5; // Process 5 images at a time
  const maxImages = 30; // Limit total images to prevent timeout

  context.log(`Processing ${Math.min(imageEntries.length, maxImages)} images in batches of ${batchSize}`);

  for (let i = 0; i < Math.min(imageEntries.length, maxImages); i += batchSize) {
    const batch = imageEntries.slice(i, i + batchSize);

    // Process batch in parallel
    const batchPromises = batch.map(async ([key, url]) => {
      try {
        // Extract container name and blob path from URL
        const urlObj = new URL(url);
        const pathParts = urlObj.pathname.split('/');
        const containerName = pathParts[1]; // First part after the leading slash

        // Extract the blob path from the URL
        let blobPath = key;

        // Try to find the blob using the key
        const containerClient = blobServiceClient.getContainerClient(containerName);
        const blobClient = containerClient.getBlobClient(blobPath);

        let blobExists = false;
        try {
          await blobClient.getProperties();
          blobExists = true;
        } catch (e) {
          // Try to extract the blob path from the URL
          const extractedPath = urlObj.pathname.substring(urlObj.pathname.indexOf('/', 1) + 1);

          // Try with the extracted path
          const extractedBlobClient = containerClient.getBlobClient(extractedPath);
          try {
            await extractedBlobClient.getProperties();
            blobPath = extractedPath;
            blobExists = true;
          } catch (e2) {
            // Skip this image if not found
            return false;
          }
        }

        if (blobExists) {
          // Download the blob to the temp directory
          const blobClient = containerClient.getBlobClient(blobPath);
          const fileName = path.basename(key);
          const filePath = path.join(tempDir, fileName);

          await blobClient.downloadToFile(filePath);

          // Store both the full path and the basename in the image map
          imageMap[key] = filePath;
          imageMap[fileName] = filePath;
          return true;
        }
        return false;
      } catch (error: any) {
        context.warn(`Failed to process image ${key}: ${error.message}`);
        return false;
      }
    });

    // Wait for batch to complete
    const results = await Promise.all(batchPromises);
    const successCount = results.filter(r => r).length;
    context.log(`Batch ${Math.floor(i / batchSize) + 1}: Downloaded ${successCount}/${batch.length} images`);
  }
}

/**
 * Creates a professional document using the docx library
 */
async function createProfessionalDocument(
  submission: SubmissionData,
  imageMap: Record<string, string>,
  context: InvocationContext
): Promise<Document> {
  // Rename context to make it accessible in nested functions
  const _context = context;
  const { payload } = submission;

  context.log(`Starting document creation with ${Object.keys(imageMap).length} images`);

  // Create document with cover page and table of contents
  const children: any[] = [];

  context.log(`Creating document structure`);

  // Get machines data and sort by Total HRN Score (highest first)
  const machines = (payload.section_2 || []).sort((a, b) => {
    const getMachineHRN = (machine: MachineSection): number => {
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0];
      if (!machineSubform || machineSubform.total_hrn === undefined) {
        return -1; // Put machines without HRN scores at the end
      }
      return Number(machineSubform.total_hrn) || -1;
    };

    const hrnA = getMachineHRN(a);
    const hrnB = getMachineHRN(b);

    // Sort in descending order (highest HRN first)
    return hrnB - hrnA;
  });

  // Add modern, visually appealing cover page with assessment summary
  children.push(
    // Company logo (large) at the top
    new Paragraph({
      children: [
        new ImageRun({
          data: fs.readFileSync(path.join(__dirname, "../images/Name-Plate-large.png")),
          transformation: {
            width: 144, // 2 inches at 72 DPI
            height: 144, // 2 inches at 72 DPI
          },
          type: "png",
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 300, // Spacing after logo
      },
    }),

    // Title with modern styling
    new Paragraph({
      text: "Machine Guarding Assessment Report",
      style: "Title",
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 400, // Extra spacing after title
      },
      border: {
        bottom: {
          color: "4472C4",
          space: 1,
          style: "single",
          size: 8,
        },
      },
    }),

    // Subtitle with facility name
    new Paragraph({
      children: [
        new TextRun({
          text: payload.formName || "Odiz Machine Guarding Assessment",
          size: 32, // 16pt
          font: "Calibri Light",
          color: "2E74B5",
        })
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 400, // Extra spacing
      },
    }),

    // Assessment details section with modern styling
    new Paragraph({
      children: [
        new TextRun({
          text: "Assessment Details",
          size: 28, // 14pt
          font: "Calibri Light",
          color: "2E74B5",
          bold: true,
        })
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        before: 200,
        after: 200,
      },
    }),

    // Details in a cleaner format
    new Paragraph({
      children: [
        new TextRun({
          text: "Facility: ",
          bold: true,
        }),
        new TextRun({
          text: payload.main_form_facility_name || "",
        }),
      ],
      alignment: AlignmentType.CENTER,
    }),
    new Paragraph({
      children: [
        new TextRun({
          text: "Assessment Date: ",
          bold: true,
        }),
        new TextRun({
          text: formatDate(payload.main_form_date),
        }),
      ],
      alignment: AlignmentType.CENTER,
    }),
    new Paragraph({
      children: [
        new TextRun({
          text: "Assessor: ",
          bold: true,
        }),
        new TextRun({
          text: `${payload.main_form_first_name || ""} ${payload.main_form_last_name || ""}`,
        }),
      ],
      alignment: AlignmentType.CENTER,
    }),
    new Paragraph({
      children: [
        new TextRun({
          text: "Contact: ",
          bold: true,
        }),
        new TextRun({
          text: `${payload.main_form_email_address || ""} | ${payload.main_form_phone_number || ""}`,
        }),
      ],
      alignment: AlignmentType.CENTER,
    }),
    new Paragraph({
      children: [
        new TextRun({
          text: "Number of Machines: ",
          bold: true,
        }),
        new TextRun({
          text: `${machines.length}`,
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 400, // Extra spacing
      },
    }),

    // Assessment summary section with modern styling
    new Paragraph({
      children: [
        new TextRun({
          text: "Assessment Summary",
          size: 28, // 14pt
          font: "Calibri Light",
          color: "2E74B5",
          bold: true,
        })
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        before: 200,
        after: 200,
      },
      border: {
        top: {
          color: "E0E0E0",
          space: 1,
          style: "single",
          size: 6,
        },
      },
    }),

    // Summary text with better formatting
    new Paragraph({
      text: "This report provides a detailed assessment of machine guarding and safety measures for the equipment evaluated. Each machine has been assessed for compliance with applicable safety standards and recommendations for improvements have been provided where necessary.",
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 120,
      },
    }),
    new Paragraph({
      text: "Machine guarding is a critical component of workplace safety, designed to protect operators and other employees from hazards created by moving machine parts, flying debris, and other potential dangers.",
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 400,
      },
    }),

    // Footer information with subtle styling
    new Paragraph({
      children: [
        new TextRun({
          text: `Submission ID: ${submission.id}`,
          size: 20, // 10pt
          color: "808080", // Gray color
        })
      ],
      alignment: AlignmentType.CENTER,
    }),
    new Paragraph({
      children: [
        new TextRun({
          text: `Report Generated: ${new Date().toLocaleDateString()}`,
          size: 20, // 10pt
          color: "808080", // Gray color
        })
      ],
      alignment: AlignmentType.CENTER,
      border: {
        top: {
          color: "E0E0E0",
          space: 1,
          style: "single",
          size: 6,
        },
      },
      spacing: {
        before: 200,
      },
    }),
    new Paragraph({
      children: [new PageBreak()]
    })
  );

  // Add a modern, visually appealing table of contents on page 2
  children.push(
    new Paragraph({
      children: [
        new Bookmark({
          id: "toc",
          children: [
            new TextRun({
              text: "Table of Contents",
              size: 32, // 16pt
              font: "Calibri Light",
              color: "2E74B5",
              bold: true,
            })
          ],
        })
      ],
      spacing: {
        after: 400, // Extra spacing
      },
      border: {
        bottom: {
          color: "4472C4",
          space: 1,
          style: "single",
          size: 6,
        },
      },
    })
  );

  // Create a list of machines with internal hyperlinks
  const machineListItems: Paragraph[] = [];

  // Add a brief description
  machineListItems.push(
    new Paragraph({
      text: "Click on a machine name to navigate directly to its assessment section. Machines are ordered by HRN risk score (highest risk first).",
      spacing: {
        after: 200,
      },
    })
  );

  // Create hyperlinks for each machine
  if (machines.length > 0) {
    machines.forEach((machine, index) => {
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0];
      if (!machineSubform) return;

      const machineTitle = machine.fields.main_form_machine_title ||
                          `Machine ${index + 1}`;
      const machineId = `machine_${index + 1}`; // Use same ID format as in the machine section

      // Get HRN score for display
      const hrnScore = machineSubform.total_hrn;
      const hrnText = hrnScore !== undefined ? ` (HRN: ${hrnScore})` : '';

      machineListItems.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `${index + 1}. `,
              bold: true,
              size: 26, // 13pt - slightly larger
            }),
            new InternalHyperlink({
              children: [
                new TextRun({
                  text: machineTitle,
                  style: "Hyperlink",
                  size: 26, // 13pt - slightly larger
                })
              ],
              anchor: machineId,
            }),
            new TextRun({
              text: hrnText,
              size: 22, // 11pt - slightly smaller for the HRN score
              color: "666666", // Gray color
            })
          ],
          spacing: {
            before: 80,
            after: 80,
          },
          indent: {
            left: 360, // Indent for list-like appearance
          }
        })
      );
    });
  } else {
    machineListItems.push(
      new Paragraph({
        text: "No machines found in this assessment.",
        spacing: {
          after: 200,
        },
      })
    );
  }

  // Add all machine list items to the document
  children.push(...machineListItems);

  // Add page break after TOC
  children.push(
    new Paragraph({
      children: [new PageBreak()]
    })
  );

  // Add assessment summary section with heading level for TOC
  children.push(
    new Paragraph({
      text: "Assessment Summary",
      style: "Heading1", // Explicitly set the style to match the heading level
      spacing: {
        before: 240,
        after: 120,
      },
      border: {
        bottom: {
          color: "4472C4",
          space: 1,
          style: "single",
          size: 6,
        },
      }
    })
  );

  // Add a brief summary paragraph
  children.push(
    new Paragraph({
      text: "This assessment provides a detailed evaluation of machine safety and compliance with relevant standards. Each machine has been assessed according to applicable requirements.",
      spacing: {
        after: 200,
      },
    })
  );

  // Add machines list with heading level for TOC
  if (machines.length > 0) {
    children.push(
      new Paragraph({
        text: "Machines Assessed",
        style: "Heading1", // Explicitly set the style to match the heading level
        spacing: {
          before: 240,
          after: 120,
        },
        border: {
          bottom: {
            color: "4472C4",
            space: 1,
            style: "single",
            size: 6,
          },
        }
      })
    );

    children.push(
      new Paragraph({
        text: "The following machines were assessed as part of this evaluation.",
        spacing: {
          after: 200,
        },
      })
    );

    // Check which columns have data
    const hasLocation = machines.some(machine => {
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};
      return machineSubform.location_of_machine && machineSubform.location_of_machine.trim() !== "";
    });

    const hasDepartment = machines.some(machine => {
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};
      return machineSubform.department && machineSubform.department.trim() !== "";
    });

    // Build header row dynamically based on available data
    const headerCells = [
      new TableCell({
        children: [new Paragraph({
          children: [new TextRun({ text: "Machine Name", bold: true })]
        })],
        shading: { fill: "F2F2F2" }, // Light gray background for header
        margins: {
          top: 120,    // 0.06 inches (6pt)
          bottom: 120, // 0.06 inches (6pt)
          left: 240,   // 0.12 inches (12pt)
          right: 240   // 0.12 inches (12pt)
        }
      }),
      new TableCell({
        children: [new Paragraph({
          children: [new TextRun({ text: "Machine Type", bold: true })]
        })],
        shading: { fill: "F2F2F2" },
        margins: {
          top: 120,
          bottom: 120,
          left: 240,
          right: 240
        }
      })
    ];

    // Add Location column only if any machine has location data
    if (hasLocation) {
      headerCells.push(
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({ text: "Location", bold: true })]
          })],
          shading: { fill: "F2F2F2" },
          margins: {
            top: 120,
            bottom: 120,
            left: 240,
            right: 240
          }
        })
      );
    }

    // Add Department column only if any machine has department data
    if (hasDepartment) {
      headerCells.push(
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({ text: "Department", bold: true })]
          })],
          shading: { fill: "F2F2F2" },
          margins: {
            top: 120,
            bottom: 120,
            left: 240,
            right: 240
          }
        })
      );
    }

    // Add machines table with modern styling
    const rows = [
      new TableRow({
        tableHeader: true,
        children: headerCells
      })
    ];

    // Add a row for each machine
    for (const machine of machines) {
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};

      // Build data cells dynamically based on which columns are included
      const dataCells = [
        new TableCell({
          children: [new Paragraph({
            children: [new TextRun({ text: machine.fields.main_form_machine_title || "Unnamed Machine" })]
          })],
          margins: {
            top: 120,
            bottom: 120,
            left: 240,
            right: 240
          }
        }),
        new TableCell({
          children: [new Paragraph(formatMachineType(machineSubform.general_machine_type))],
          margins: {
            top: 120,
            bottom: 120,
            left: 240,
            right: 240
          }
        })
      ];

      // Add Location cell only if Location column is included
      if (hasLocation) {
        dataCells.push(
          new TableCell({
            children: [new Paragraph(machineSubform.location_of_machine || "")],
            margins: {
              top: 120,
              bottom: 120,
              left: 240,
              right: 240
            }
          })
        );
      }

      // Add Department cell only if Department column is included
      if (hasDepartment) {
        dataCells.push(
          new TableCell({
            children: [new Paragraph(machineSubform.department || "")],
            margins: {
              top: 120,
              bottom: 120,
              left: 240,
              right: 240
            }
          })
        );
      }

      rows.push(
        new TableRow({
          children: dataCells
        })
      );
    }

    children.push(
      new Table({
        width: { size: 100, type: WidthType.PERCENTAGE },
        rows,
        borders: {
          top: { style: "single", size: 1, color: "auto" },
          bottom: { style: "single", size: 1, color: "auto" },
          left: { style: "single", size: 1, color: "auto" },
          right: { style: "single", size: 1, color: "auto" },
          insideHorizontal: { style: "single", size: 1, color: "auto" },
          insideVertical: { style: "single", size: 1, color: "auto" }
        }
      }),
      new Paragraph({
        children: [new PageBreak()]
      })
    );

    // Add detailed information for each machine
    context.log(`Processing ${machines.length} machines`);
    for (let i = 0; i < machines.length; i++) {
      const machine = machines[i];
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};
      const machineTitle = machine.fields.main_form_machine_title || `Machine ${i + 1}`;

      context.log(`Processing machine ${i + 1}/${machines.length}: ${machineTitle}`);

      // Log machine type and assessment level for debugging
      if (machineSubform.general_machine_type) {
        context.log(`Machine type: ${machineSubform.general_machine_type[0]}`);
      }
      if (machineSubform.assessment_level) {
        context.log(`Assessment level: ${machineSubform.assessment_level[0]}`);
      }

      // Create a unique ID for this machine
      const machineId = `machine_${i + 1}`;

      // Add machine heading with bookmark for navigation
      children.push(
        new Paragraph({
          children: [
            new Bookmark({
              id: machineId,
              children: [
                new TextRun({
                  text: machineTitle,
                  size: 32, // 16pt
                  font: "Calibri Light",
                  color: "2E74B5", // Modern blue
                })
              ],
            }),
          ],
          style: "Heading1", // Explicitly set the style to match the heading level
          spacing: {
            before: 240,
            after: 120,
          },
          border: {
            bottom: {
              color: "4472C4",
              space: 1,
              style: "single",
              size: 6,
            },
          }
        })
      );

      // Add machine overview section with heading level for TOC
      children.push(
        new Paragraph({
          text: "Machine Overview",
          style: "Heading2", // Explicitly set the style to match the heading level
          spacing: {
            before: 200,
            after: 120,
          }
        }),
        new Table({
          width: { size: 100, type: WidthType.PERCENTAGE },
          rows: [
            createTableRow("Type", formatMachineType(machineSubform.general_machine_type)),
            createTableRow("ID", machineSubform.machine_name_and_id || "Not specified"),
            createTableRow("Location", machineSubform.location_of_machine || "Not specified"),
            createTableRow("Department", machineSubform.department || "Not specified")
          ],
          borders: {
            top: { style: "single", size: 1, color: "auto" },
            bottom: { style: "single", size: 1, color: "auto" },
            left: { style: "single", size: 1, color: "auto" },
            right: { style: "single", size: 1, color: "auto" },
            insideHorizontal: { style: "single", size: 1, color: "auto" },
            insideVertical: { style: "single", size: 1, color: "auto" }
          }
        })
      );

      // Add assessment information section
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "Assessment Information",
              size: 28, // 14pt
              font: "Calibri Light",
              color: "2E74B5",
              bold: true,
            })
          ],
          spacing: {
            before: 200,
            after: 120,
          }
        }),
        new Table({
          width: { size: 100, type: WidthType.PERCENTAGE },
          rows: [
            createTableRow("Assessment Level", formatAssessmentLevel(machineSubform.assessment_level)),
            createTableRow("Machine in Operation", formatYesNo(machineSubform.is_machine_in_operation)),
            createTableRow("Machine Observed", formatYesNo(machineSubform.was_machine_observed_during_observation)),
            createTableRow("Known Injuries", formatYesNo(machineSubform.known_injuries_associated_with_machine))
          ],
          borders: {
            top: { style: "single", size: 1, color: "auto" },
            bottom: { style: "single", size: 1, color: "auto" },
            left: { style: "single", size: 1, color: "auto" },
            right: { style: "single", size: 1, color: "auto" },
            insideHorizontal: { style: "single", size: 1, color: "auto" },
            insideVertical: { style: "single", size: 1, color: "auto" }
          }
        })
      );

      // Add machine specifications section with heading level for TOC
      children.push(
        new Paragraph({
          text: "Machine Specifications",
          style: "Heading2", // Explicitly set the style to match the heading level
          spacing: {
            before: 200,
            after: 120,
          }
        }),
        new Table({
          width: { size: 100, type: WidthType.PERCENTAGE },
          rows: [
            createTableRow("Manufacturer", machineSubform.machine_manufacturer || "Not specified"),
            createTableRow("Model Number", machineSubform.machine_model_number || "Not specified"),
            createTableRow("Serial Number", machineSubform.machine_serial_number || "Not specified"),
            createTableRow("Operator Names", machineSubform.operator_names || "Not specified")
          ],
          borders: {
            top: { style: "single", size: 1, color: "auto" },
            bottom: { style: "single", size: 1, color: "auto" },
            left: { style: "single", size: 1, color: "auto" },
            right: { style: "single", size: 1, color: "auto" },
            insideHorizontal: { style: "single", size: 1, color: "auto" },
            insideVertical: { style: "single", size: 1, color: "auto" }
          }
        })
      );

      // Add machine description if available
      if (machineSubform.machine_description) {
        children.push(
          new Paragraph({
            text: "Machine Description",
            style: "Heading2", // Explicitly set the style to match the heading level
            spacing: {
              before: 200,
              after: 120,
            }
          }),
          new Paragraph({
            text: machineSubform.machine_description
          })
        );
      }

      // Add General Assessment sections based on filled fields, not assessment level

      // OSHA-Level General Assessment - Check if any OSHA fields are filled
      context.log(`Checking for OSHA assessment fields for machine: ${machineTitle}`);
      const oshaGeneralRows: TableRow[] = [];

      // Define OSHA general assessment questions and their field names
      const oshaQuestions = [
        { field: 'are_moving_parts_guarded', question: 'Are moving parts guarded?' },
        { field: 'are_guards_secured', question: 'Are guards secured?' },
        { field: 'are_guards_made_of_durable_material', question: 'Are guards made of durable material?' },
        { field: 'are_guards_properly_maintained', question: 'Are guards properly maintained?' },
        { field: 'are_rotating_parts_guarded', question: 'Are rotating parts guarded?' },
        { field: 'are_points_of_operation_safeguarded', question: 'Are points of operation safeguarded?' },
        { field: 'is_there_evidence_of_guard_removal_or_tampering', question: 'Is there evidence of guard removal or tampering?' },
        { field: 'are_hand_tools_used_to_feed_or_remove_parts', question: 'Are hand tools used to feed or remove parts?' },
        { field: 'are_warning_signs_or_labels_present', question: 'Are warning signs or labels present?' },
        { field: 'are_electrical_panels_and_wiring_compliant', question: 'Are electrical panels and wiring compliant?' }
      ];

      // Add rows for OSHA questions that have values
      for (const q of oshaQuestions) {
        if (machineSubform[q.field] !== undefined && machineSubform[q.field] !== null) {
          oshaGeneralRows.push(createAssessmentRow(q.question, machineSubform[q.field]));
        }
      }

      // Only add the OSHA section if we found filled fields
      if (oshaGeneralRows.length > 0) {
        children.push(
          new Paragraph({
            text: "OSHA-Level General Assessment",
            style: "Heading2", // Explicitly set the style to match the heading level
            spacing: {
              before: 200,
              after: 120,
            }
          }),
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: oshaGeneralRows,
            borders: {
              top: { style: "single", size: 1, color: "auto" },
              bottom: { style: "single", size: 1, color: "auto" },
              left: { style: "single", size: 1, color: "auto" },
              right: { style: "single", size: 1, color: "auto" },
              insideHorizontal: { style: "single", size: 1, color: "auto" },
              insideVertical: { style: "single", size: 1, color: "auto" }
            }
          })
        );
      }

      // ANSI/ISO-Level General Assessment - Check if any ANSI/ISO fields are filled
      const ansiIsoGeneralRows: TableRow[] = [];

      // Define ANSI/ISO general assessment questions and their field names
      const ansiIsoQuestions = [
        { field: 'has_risk_assessment_been_performed_iso_12100', question: 'Has risk assessment been performed (ISO 12100)?' },
        { field: 'has_pl_level_been_assigned', question: 'Has PL level been assigned?' },
        { field: 'is_there_safety_control_system_in_place', question: 'Is there safety control system in place?' },
        { field: 'is_the_safety_control_system_validated_per_iso_13849_2_or_iec_62061', question: 'Is the safety control system validated per ISO 13849-2 or IEC 62061?' },
        { field: 'are_emergency_strop_devices_present_and_accessible', question: 'Are emergency stop devices present and accessible?' },
        { field: 'are_restart_and_reset_function_appropriately_managed', question: 'Are restart and reset function appropriately managed?' },
        { field: 'are_lockout_tagout_provisions_documented_and_functional', question: 'Are lockout/tagout provisions documented and functional?' },
        { field: 'are_operator_controls_ergonomically_positioned_and_clearly_marked', question: 'Are operator controls ergonomically positioned and clearly marked?' },
        { field: 'are_safeguards_designed_to_minimize_defeatability_or_bypass', question: 'Are safeguards designed to minimize defeatability or bypass?' },
        { field: 'has_residual_risk_been_addressed_with_training_or_PPE', question: 'Has residual risk been addressed with training or PPE?' }
      ];

      // Add rows for ANSI/ISO questions that have values
      for (const q of ansiIsoQuestions) {
        if (machineSubform[q.field] !== undefined && machineSubform[q.field] !== null) {
          ansiIsoGeneralRows.push(createAssessmentRow(q.question, machineSubform[q.field]));
        }
      }

      // Only add the ANSI/ISO section if we found filled fields
      if (ansiIsoGeneralRows.length > 0) {
        children.push(
          new Paragraph({
            text: "ANSI/ISO-Level General Assessment",
            style: "Heading2", // Explicitly set the style to match the heading level
            spacing: {
              before: 200,
              after: 120,
            }
          }),
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: ansiIsoGeneralRows,
            borders: {
              top: { style: "single", size: 1, color: "auto" },
              bottom: { style: "single", size: 1, color: "auto" },
              left: { style: "single", size: 1, color: "auto" },
              right: { style: "single", size: 1, color: "auto" },
              insideHorizontal: { style: "single", size: 1, color: "auto" },
              insideVertical: { style: "single", size: 1, color: "auto" }
            }
          })
        );
      }

      // Add Machine-Specific Assessment based on machine type - Generic approach
      context.log(`Checking for machine-specific assessment fields for machine: ${machineTitle}`);
      if (machineSubform.general_machine_type) {
        const machineType = machineSubform.general_machine_type[0];
        context.log(`Processing machine-specific assessment for type: ${machineType}`);

        // Get machine type display name
        const machineTypeMap: Record<string, string> = {
          "general": "General Machine",
          "complex": "Complex Machine System",
          "abb_wheel": "Abrasive Wheel Grinder / Bench or Pedestal Stand",
          "band_saw": "Bandsaw",
          "cnc": "CNC Machine",
          "drill_press": "Drill Press",
          "honing": "Honing Machine",
          "boring": "Horizontal Boring Machine",
          "horizontal_lathe": "Horizontal Lathe",
          "hyd_press": "Hydraulic Press",
          "ironworker": "Ironworker",
          "milling_machine": "Manual Mill",
          "mech_press": "Mechanical Press",
          "pipe_bender": "Pipe Bender",
          "pipe_thread": "Pipe Threading Machine",
          "press_brake": "Press Break",
          "shear": "Shear",
          "surf_grinder": "Surface Grinder",
          "vtl": "Vertical Turret Lathe",
          "other": "Other",
          // Legacy mappings for backward compatibility
          "vertical_lathe": "Vertical Lathe",
          "horizontal_mill": "Horizontal Mill",
          "vertical_mill": "Vertical Mill",
          "grinder": "Grinder",
          "punch_press": "Punch Press",
          "robot": "Robot",
          "conveyor": "Conveyor",
          "broaching": "Broaching Machine",
          "edm": "EDM Machine",
          "laser": "Laser Machine"
        };

        const machineTypeName = machineTypeMap[machineType] || machineType;

        // Define known machine-specific questions for different machine types
        const knownMachineQuestions: Record<string, Array<{field: string, question: string}>> = {
          "drill_press": [
            { field: 'is_the_spindle_and_chuck_guarded_to_prevent_contact', question: 'Is the spindle and chuck guarded to prevent contact?' },
            { field: 'is_the_drill_bit_point_of_operation_guarded_to_prevent_accidental_contact', question: 'Is the drill bit point of operation guarded to prevent accidental contact?' },
            { field: 'is_the_belt_and_pulley_system_enclosed_with_a_fixed_or_interlocked_guard', question: 'Is the belt and pulley system enclosed with a fixed or interlocked guard?' },
            { field: 'is_the_workpiece_securely_clamped_or_held_in_a_drill_press_vice_during_operation', question: 'Is the workpiece securely clamped or held in a drill press vice during operation?' },
            { field: 'are_operators_prevented_from_wearing_loose_clothing_jewelry_or_gloves_while_operating', question: 'Are operators prevented from wearing loose clothing, jewelry, or gloves while operating?' },
            { field: 'are_on_off_controls_within_easy_reach_of_operator', question: 'Are on/off controls within easy reach of operator?' },
            { field: 'are_rotating_parts_clearly_identified_as_hazardous', question: 'Are rotating parts clearly identified as hazardous?' },
            { field: 'is_the_floor_area_around_drill_press_kept_clear_and_dry', question: 'Is the floor area around drill press kept clear and dry?' },
            { field: 'are_all_adjustments_designed_so_they_can_be_made_safely', question: 'Are all adjustments designed so they can be made safely?' },
            { field: 'is_appropriate_signage_or_instructional_labeling_posted_on_or_near_drill_press', question: 'Is appropriate signage or instructional labeling posted on or near drill press?' }
          ],
          "pipe_thread": [
            { field: 'field_361', question: 'Is the chuck and rotating pipe guarded?' },
            { field: 'field_362', question: 'Is the threading die head guarded?' },
            { field: 'field_363', question: 'Are foot pedals guarded against accidental operation?' },
            { field: 'field_364', question: 'Is the pipe support system adequate and stable?' },
            { field: 'field_365', question: 'Are cutting oils contained to prevent slips?' },
            { field: 'field_366', question: 'Is there an emergency stop device within reach?' },
            { field: 'field_367', question: 'Are operators trained on proper pipe handling?' },
            { field: 'field_368', question: 'Is proper PPE required and used?' },
            { field: 'field_369', question: 'Is the machine secured to prevent tipping?' },
            { field: 'field_370', question: 'Are electrical components protected from cutting oils?' }
          ]
          // Additional machine types can be added here as they are identified
        };

        // List of fields to exclude from machine-specific questions
        const excludeFields = [
          // General fields
          'general_machine_type', 'assessment_level', 'machine_name_and_id', 'location_of_machine',
          'department', 'machine_manufacturer', 'machine_model_number', 'machine_serial_number',
          'operator_names', 'machine_description', 'is_machine_in_operation',
          'was_machine_observed_during_observation', 'known_injuries_associated_with_machine',
          'section_40', 'reserved_instanceGuid', 'Hide_General_Question_Sections',
          'Hide_Show_Machine_Questions_and_Solutions', 'HRN___PLR', '_Other__Solutions',
          'CURRENT_USER_VAR', 'total_hrn', 'field_score',

          // Risk assessment fields
          'field_DPH', 'field_NP', 'field_FE', 'field_LO', 'field_S', 'field_F', 'field_P',

          // Other non-question fields
          ...oshaQuestions.map(q => q.field),
          ...ansiIsoQuestions.map(q => q.field)
        ];

        // Fields that start with these prefixes should be excluded
        const excludePrefixes = ['photo_', 'listpicker_', 'multiline_'];

        // Create a map to track which assessment types we've found questions for
        const assessmentTypes: Record<string, TableRow[]> = {
          'OSHA': [],
          'ANSI/ISO': []
        };

        // First, check for known machine-specific questions
        if (knownMachineQuestions[machineType]) {
          for (const q of knownMachineQuestions[machineType]) {
            if (machineSubform[q.field] !== undefined && machineSubform[q.field] !== null) {
              // For drill press, we know these are OSHA questions
              if (machineType === 'drill_press') {
                assessmentTypes['OSHA'].push(createAssessmentRow(q.question, machineSubform[q.field]));
              }
              // For pipe threader, we know these are ANSI/ISO questions
              else if (machineType === 'pipe_thread') {
                assessmentTypes['ANSI/ISO'].push(createAssessmentRow(q.question, machineSubform[q.field]));
              }
              // For other machine types, we don't know which assessment type they belong to
              else {
                // Default to OSHA if we don't know
                assessmentTypes['OSHA'].push(createAssessmentRow(q.question, machineSubform[q.field]));
              }
            }
          }
        }

        // Then, look for any other machine-specific questions we don't know about
        for (const key in machineSubform) {
          // Skip excluded fields and fields with excluded prefixes
          if (excludeFields.includes(key) || excludePrefixes.some(prefix => key.startsWith(prefix))) {
            continue;
          }

          const value = machineSubform[key];

          // Skip empty or undefined values
          if (value === undefined || value === null) {
            continue;
          }

          // Check if this is a known question we've already processed
          const isKnownQuestion = knownMachineQuestions[machineType]?.some(q => q.field === key) || false;
          if (isKnownQuestion) {
            continue;
          }

          // Try to get the question from the form definition first
          let question = fieldToQuestionMap[key];

          if (!question) {
            // Fallback: Convert key to a human-readable question
            question = key
              // Replace underscores with spaces
              .replace(/_/g, ' ')
              // Insert spaces before capital letters
              .replace(/([A-Z])/g, ' $1')
              // Capitalize first letter
              .replace(/^\w/, c => c.toUpperCase());

            // Add question mark if not present
            if (!question.endsWith('?')) {
              question += '?';
            }
          }

          // For unknown questions, try to determine which assessment type they belong to
          // This is a heuristic and may not always be correct
          if (key.includes('ansi') || key.includes('iso')) {
            assessmentTypes['ANSI/ISO'].push(createAssessmentRow(question, value));
          } else {
            // Default to OSHA if we can't determine
            assessmentTypes['OSHA'].push(createAssessmentRow(question, value));
          }
        }

        // Add sections for each assessment type that has questions
        for (const [assessmentType, rows] of Object.entries(assessmentTypes)) {
          if (rows.length > 0) {
            children.push(
              new Paragraph({
                text: `${machineTypeName} – ${assessmentType} Assessment`,
                style: "Heading2", // Explicitly set the style to match the heading level
                spacing: {
                  before: 200,
                  after: 120,
                }
              }),
              new Table({
                width: { size: 100, type: WidthType.PERCENTAGE },
                rows,
                borders: {
                  top: { style: "single", size: 1, color: "auto" },
                  bottom: { style: "single", size: 1, color: "auto" },
                  left: { style: "single", size: 1, color: "auto" },
                  right: { style: "single", size: 1, color: "auto" },
                  insideHorizontal: { style: "single", size: 1, color: "auto" },
                  insideVertical: { style: "single", size: 1, color: "auto" }
                }
              })
            );
          }
        }
      }

      // Add Risk Assessment Information if available
      context.log(`Checking for risk assessment data for machine: ${machineTitle}`);
      // Check for HRN data (regardless of HRN___PLR flag)
      if (machineSubform.total_hrn !== undefined ||
          (machineSubform.field_DPH && machineSubform.field_DPH.length > 0) ||
          (machineSubform.field_NP && machineSubform.field_NP.length > 0) ||
          (machineSubform.field_FE && machineSubform.field_FE.length > 0) ||
          (machineSubform.field_LO && machineSubform.field_LO.length > 0)) {

        context.log(`Found HRN risk assessment data for machine: ${machineTitle}`);
        if (machineSubform.total_hrn !== undefined) {
          context.log(`HRN Score: ${machineSubform.total_hrn}`);
        }

        children.push(
          new Paragraph({
            text: "Risk Determination (HRN Methodology)",
            style: "Heading2", // Explicitly set the style to match the heading level
            spacing: {
              before: 200,
              after: 120,
            }
          })
        );

        // Helper function to get HRN factor text and value
        function getHrnFactorText(factor: string, value: string | undefined): string {
          if (!value) return "Not specified";

          // Map of HRN factor values to their text descriptions
          const hrnFactorMap: Record<string, Record<string, string>> = {
            'DPH': {
              '0.1': '0.1 - Scratch, bruise, minor cut',
              '0.5': '0.5 - Laceration, mild ill-health effect',
              '1': '1 - Break minor bone or minor illness (temporary)',
              '2': '2 - Break major bone or major illness (temporary)',
              '4': '4 - Loss of 1 limb, eye, hearing loss (permanent)',
              '8': '8 - Loss of 2 limbs, eyes (permanent)',
              '15': '15 - Fatality'
            },
            'NP': {
              '1': '1 - 1-2 people',
              '2': '2 - 3-7 people',
              '4': '4 - 8-15 people',
              '8': '8 - 16-50 people',
              '12': '12 - 51+ people'
            },
            'FE': {
              '0.5': '0.5 - Annually',
              '1': '1 - Monthly',
              '1.5': '1.5 - Weekly',
              '2.5': '2.5 - Daily',
              '4': '4 - Hourly',
              '5': '5 - Constantly'
            },
            'LO': {
              '0.1': '0.1 - Almost impossible',
              '0.5': '0.5 - Highly unlikely',
              '1': '1 - Unlikely',
              '3': '3 - Likely',
              '6': '6 - Very likely',
              '10': '10 - Certain'
            }
          };

          return hrnFactorMap[factor]?.[value] || `${value} - Unknown description`;
        }

        // Create HRN table
        const hrnRows = [
          createTableRow("Degree of Possible Harm (DPH)", getHrnFactorText('DPH', machineSubform.field_DPH ? machineSubform.field_DPH[0] : undefined)),
          createTableRow("Number of Persons at Risk (NP)", getHrnFactorText('NP', machineSubform.field_NP ? machineSubform.field_NP[0] : undefined)),
          createTableRow("Frequency of Exposure (FE)", getHrnFactorText('FE', machineSubform.field_FE ? machineSubform.field_FE[0] : undefined)),
          createTableRow("Likelihood of Occurrence (LO)", getHrnFactorText('LO', machineSubform.field_LO ? machineSubform.field_LO[0] : undefined)),
          createTableRow("Total HRN Score", machineSubform.total_hrn !== undefined ? machineSubform.total_hrn.toString() : "Not calculated")
        ];

        children.push(
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: hrnRows,
            borders: {
              top: { style: "single", size: 1, color: "auto" },
              bottom: { style: "single", size: 1, color: "auto" },
              left: { style: "single", size: 1, color: "auto" },
              right: { style: "single", size: 1, color: "auto" },
              insideHorizontal: { style: "single", size: 1, color: "auto" },
              insideVertical: { style: "single", size: 1, color: "auto" }
            }
          })
        );

        // Add HRN risk level explanation
        let hrnRiskLevel = "";

        if (machineSubform.total_hrn !== undefined) {
          const hrnScore = Number(machineSubform.total_hrn);

          let hrnBackgroundColor: string;
          let hrnTextColor: string;
          let hrnBorderColor: string;

          if (hrnScore < 2) {
            hrnRiskLevel = "Low Risk";
            hrnBackgroundColor = "D4EDDA"; // Light green background
            hrnTextColor = "155724"; // Dark green text
            hrnBorderColor = "28A745"; // Medium green border
          } else if (hrnScore < 50) {
            hrnRiskLevel = "Medium Risk";
            hrnBackgroundColor = "FFF3CD"; // Light yellow background
            hrnTextColor = "856404"; // Dark yellow/brown text
            hrnBorderColor = "FFC107"; // Medium yellow border
          } else {
            hrnRiskLevel = "High Risk";
            hrnBackgroundColor = "F8D7DA"; // Light red background
            hrnTextColor = "721C24"; // Dark red text
            hrnBorderColor = "DC3545"; // Medium red border
          }

          // Make the risk level result more prominent with colored background
          children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: "RISK LEVEL: ",
                  bold: true,
                  size: 28, // 14pt
                  color: hrnTextColor
                }),
                new TextRun({
                  text: hrnRiskLevel,
                  bold: true,
                  size: 28, // 14pt
                  color: hrnTextColor
                })
              ],
              border: {
                top: { style: "single", size: 2, color: hrnBorderColor },
                bottom: { style: "single", size: 2, color: hrnBorderColor },
                left: { style: "single", size: 2, color: hrnBorderColor },
                right: { style: "single", size: 2, color: hrnBorderColor },
              },
              shading: {
                fill: hrnBackgroundColor
              },
              alignment: AlignmentType.CENTER,
              spacing: {
                before: 120,
                after: 120,
                line: 360, // 1.5 times the font size
              }
            })
          );

          // Add simplified HRN explanation
          children.push(
            new Paragraph({
              text: "The Hazard Rating Number (HRN) is calculated by multiplying the following factors:",
              spacing: {
                before: 120,
                after: 20,
              },
            }),
            new Paragraph({
              text: "HRN = DPH × NP × FE × LO",
              spacing: {
                before: 0,
                after: 40,
              },
            }),
            new Paragraph({
              text: "Risk Level Categories:",
              spacing: {
                before: 0,
                after: 20,
              },
            }),
            new Paragraph({
              text: "• HRN < 2: Low Risk",
              spacing: {
                before: 0,
                after: 10,
              },
            }),
            new Paragraph({
              text: "• HRN 2-49: Medium Risk",
              spacing: {
                before: 0,
                after: 10,
              },
            }),
            new Paragraph({
              text: "• HRN ≥ 50: High Risk",
              spacing: {
                before: 0,
                after: 40,
              },
            })
          );
        }
      }

      // Check for PLr data - only include for ANSI/ISO assessment level
      const isAnsiIsoAssessment = machineSubform.assessment_level &&
                                 machineSubform.assessment_level.includes("ansi-iso");

      if (isAnsiIsoAssessment &&
          (machineSubform.field_score !== undefined ||
           (machineSubform.field_S && machineSubform.field_S.length > 0) ||
           (machineSubform.field_F && machineSubform.field_F.length > 0) ||
           (machineSubform.field_P && machineSubform.field_P.length > 0))) {

        context.log(`Found PLr assessment data for machine: ${machineTitle}`);
        if (machineSubform.field_score !== undefined) {
          context.log(`PLr Score: ${machineSubform.field_score}`);
        }

        children.push(
          new Paragraph({
            text: "Performance Level Required (PLr Determination)",
            style: "Heading2", // Explicitly set the style to match the heading level
            spacing: {
              before: 200,
              after: 120,
            }
          })
        );

        // Helper function to get PLr factor text
        function getPlrFactorText(factor: string, value: string | undefined): string {
          if (!value) return "Not specified";

          // Map of PLr factor values to their text descriptions
          const plrFactorMap: Record<string, Record<string, string>> = {
            'S': {
              '3': 'S1 - Slight injury',
              '4': 'S2 - Serious injury'
            },
            'F': {
              '1': 'F1 - Seldom exposure',
              '2': 'F2 - Frequent exposure'
            },
            'P': {
              '3': 'P1 - Possible to avoid',
              '5': 'P2 - Scarcely possible to avoid'
            }
          };

          return plrFactorMap[factor]?.[value] || "Not specified";
        }

        // Log PLr field values for debugging
        context.log(`PLr S value: ${machineSubform.field_S ? machineSubform.field_S[0] : 'undefined'}`);
        context.log(`PLr F value: ${machineSubform.field_F ? machineSubform.field_F[0] : 'undefined'}`);
        context.log(`PLr P value: ${machineSubform.field_P ? machineSubform.field_P[0] : 'undefined'}`);

        // Check all possible field names for P
        const possiblePFields = ['field_P', 'field_p', 'P', 'p', 'possibility', 'Possibility'];
        for (const fieldName of possiblePFields) {
          if (machineSubform[fieldName]) {
            context.log(`Found P field with name: ${fieldName}, value: ${JSON.stringify(machineSubform[fieldName])}`);
          }
        }

        // Log all field names in machineSubform for debugging
        context.log('All field names in machineSubform:');
        for (const key in machineSubform) {
          if (key.toLowerCase().includes('p') && typeof machineSubform[key] !== 'function') {
            context.log(`Field ${key}: ${JSON.stringify(machineSubform[key])}`);
          }
        }

        // Create PLr table with more robust field access
        const pValue = machineSubform.field_P ? machineSubform.field_P[0] :
                      (machineSubform.P ? machineSubform.P[0] : undefined);

        const plrRows = [
          createTableRow("Severity of Injury (S)", getPlrFactorText('S', machineSubform.field_S ? machineSubform.field_S[0] : undefined)),
          createTableRow("Frequency of Exposure (F)", getPlrFactorText('F', machineSubform.field_F ? machineSubform.field_F[0] : undefined)),
          createTableRow("Possibility of Avoidance (P)", getPlrFactorText('P', pValue))
        ];

        children.push(
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: plrRows,
            borders: {
              top: { style: "single", size: 1, color: "auto" },
              bottom: { style: "single", size: 1, color: "auto" },
              left: { style: "single", size: 1, color: "auto" },
              right: { style: "single", size: 1, color: "auto" },
              insideHorizontal: { style: "single", size: 1, color: "auto" },
              insideVertical: { style: "single", size: 1, color: "auto" }
            }
          })
        );

        // Calculate PLr level based on S, F, and P values
        let plrLevel = "";
        let plrBackgroundColor = "";
        let plrTextColor = "";
        let plrBorderColor = "";

        // Calculate the sum of S, F, and P values
        let totalScore = 0;

        if (machineSubform.field_S && machineSubform.field_S.length > 0) {
          totalScore += Number(machineSubform.field_S[0]);
        }

        if (machineSubform.field_F && machineSubform.field_F.length > 0) {
          totalScore += Number(machineSubform.field_F[0]);
        }

        // Use the same pValue for calculation
        if (pValue) {
          totalScore += Number(pValue);
          context.log(`Adding P value to total score: ${pValue}, new total: ${totalScore}`);
        } else if (machineSubform.field_P && machineSubform.field_P.length > 0) {
          totalScore += Number(machineSubform.field_P[0]);
          context.log(`Adding field_P value to total score: ${machineSubform.field_P[0]}, new total: ${totalScore}`);
        }

        // Determine PLr level based on the total score with better colors
        if (totalScore === 7) {
          plrLevel = "A";
          plrBackgroundColor = "CCE5FF"; // Light blue background
          plrTextColor = "003D82"; // Dark blue text
          plrBorderColor = "0070C0"; // Medium blue border
        } else if (totalScore === 8) {
          plrLevel = "B";
          plrBackgroundColor = "D4EDDA"; // Light green background
          plrTextColor = "155724"; // Dark green text
          plrBorderColor = "28A745"; // Medium green border
        } else if (totalScore === 9) {
          plrLevel = "C";
          plrBackgroundColor = "FFF3CD"; // Light yellow background
          plrTextColor = "856404"; // Dark yellow/brown text
          plrBorderColor = "FFC107"; // Medium yellow border
        } else if (totalScore === 10) {
          plrLevel = "D";
          plrBackgroundColor = "FFE5CC"; // Light orange background
          plrTextColor = "8B4513"; // Dark orange/brown text
          plrBorderColor = "FD7E14"; // Medium orange border
        } else if (totalScore === 11) {
          plrLevel = "E";
          plrBackgroundColor = "F8D7DA"; // Light red background
          plrTextColor = "721C24"; // Dark red text
          plrBorderColor = "DC3545"; // Medium red border
        } else {
          plrLevel = "Not calculated";
          plrBackgroundColor = "F8F9FA"; // Light gray background
          plrTextColor = "495057"; // Dark gray text
          plrBorderColor = "6C757D"; // Medium gray border
        }

        // Add the calculated score to the table
        plrRows.push(
          createTableRow("Performance Level Required (PLr)", plrLevel)
        );

        // Make the PLr result more prominent with colored background
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: "PERFORMANCE LEVEL REQUIRED: ",
                bold: true,
                size: 28, // 14pt
                color: plrTextColor
              }),
              new TextRun({
                text: plrLevel,
                bold: true,
                size: 28, // 14pt
                color: plrTextColor
              })
            ],
            border: {
              top: { style: "single", size: 2, color: plrBorderColor },
              bottom: { style: "single", size: 2, color: plrBorderColor },
              left: { style: "single", size: 2, color: plrBorderColor },
              right: { style: "single", size: 2, color: plrBorderColor },
            },
            shading: {
              fill: plrBackgroundColor
            },
            alignment: AlignmentType.CENTER,
            spacing: {
              before: 120,
              after: 120,
              line: 360, // 1.5 times the font size
            }
          })
        );
      }

      // Add machine photos section with placeholders
      const photoSections = [
        { title: "Machine Nameplate", photos: machineSubform.photo_of_machine_nameplate },
        { title: "Machine ID", photos: machineSubform.photo_of_machine_id_num },
        { title: "Machine Front View", photos: machineSubform.photo_of_machine_front },
        { title: "Machine Right View", photos: machineSubform.photo_of_machine_right },
        { title: "Machine Back View", photos: machineSubform.photo_of_machine_back },
        { title: "Machine Left View", photos: machineSubform.photo_of_machine_left }
      ];

      if (photoSections.some(section => section.photos && section.photos.length > 0)) {
        children.push(
          new Paragraph({
            text: "Machine Photos",
            heading: HeadingLevel.HEADING_2, // Use HeadingLevel enum as recommended
            spacing: {
              before: 200,
              after: 120,
            }
          })
        );

        // Group photos into pairs for side-by-side display
        const processedSections = [];

        // First, collect all valid photo sections
        for (const section of photoSections) {
          if (section.photos && section.photos.length > 0) {
            for (const photoData of section.photos) {
              if (imageMap[photoData.photo]) {
                const imagePath = imageMap[photoData.photo];
                if (fs.existsSync(imagePath)) {
                  processedSections.push({
                    title: section.title,
                    photoData,
                    imagePath
                  });
                }
              }
            }
          }
        }

        // Process photos in pairs
        for (let i = 0; i < processedSections.length; i += 2) {
          const leftSection = processedSections[i];
          const rightSection = i + 1 < processedSections.length ? processedSections[i + 1] : null;

          // Create title paragraph(s)
          if (rightSection) {
            // If we have two images, create a table for the titles
            const titleTable = new Table({
              width: { size: 100, type: WidthType.PERCENTAGE },
              rows: [
                new TableRow({
                  children: [
                    new TableCell({
                      children: [
                        new Paragraph({
                          children: [
                            new TextRun({
                              text: leftSection.title,
                              size: 26, // 13pt
                              font: "Calibri Light",
                              color: "2E74B5",
                              bold: true,
                            })
                          ],
                        })
                      ],
                      borders: {
                        top: { style: "none" },
                        bottom: { style: "none" },
                        left: { style: "none" },
                        right: { style: "none" },
                      }
                    }),
                    new TableCell({
                      children: [
                        new Paragraph({
                          children: [
                            new TextRun({
                              text: rightSection.title,
                              size: 26, // 13pt
                              font: "Calibri Light",
                              color: "2E74B5",
                              bold: true,
                            })
                          ],
                        })
                      ],
                      borders: {
                        top: { style: "none" },
                        bottom: { style: "none" },
                        left: { style: "none" },
                        right: { style: "none" },
                      }
                    })
                  ]
                })
              ],
              borders: {
                top: { style: "none" },
                bottom: { style: "none" },
                left: { style: "none" },
                right: { style: "none" },
                insideHorizontal: { style: "none" },
                insideVertical: { style: "none" }
              }
            });

            children.push(
              new Paragraph({
                spacing: {
                  before: 160,
                  after: 0,
                },
              }),
              titleTable,
              new Paragraph({
                spacing: {
                  before: 0,
                  after: 40,
                },
              })
            );
          } else {
            // If we only have one image, create a simple paragraph
            children.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: leftSection.title,
                    size: 26, // 13pt
                    font: "Calibri Light",
                    color: "2E74B5",
                    bold: true,
                  })
                ],
                spacing: {
                  before: 160,
                  after: 40,
                },
              })
            );
          }

          // Process the left image
          try {
            const leftImageBuffer = fs.readFileSync(leftSection.imagePath);

            // Default dimensions - smaller for side-by-side
            let leftWidth = 250;
            let leftHeight = 180;

            try {
              // Get the image dimensions to maintain aspect ratio
              const dimensions = sizeOf(Buffer.from(leftImageBuffer));
              const maxWidth = 250; // Smaller max width for side-by-side

              if (dimensions && dimensions.width && dimensions.height) {
                // Maintain aspect ratio
                const aspectRatio = dimensions.width / dimensions.height;
                leftWidth = maxWidth;
                leftHeight = Math.round(leftWidth / aspectRatio);
              }
            } catch (error: any) {
              _context.warn(`Error getting dimensions for image: ${error.message}`);
            }

            // Create a table for the images
            const imageTable = new Table({
              width: { size: 100, type: WidthType.PERCENTAGE },
              rows: [
                new TableRow({
                  children: [
                    new TableCell({
                      children: [
                        new Paragraph({
                          children: [
                            new ImageRun({
                              data: leftImageBuffer,
                              transformation: {
                                width: leftWidth,
                                height: leftHeight
                              },
                              type: "jpg"
                            })
                          ],
                          alignment: AlignmentType.CENTER
                        })
                      ],
                      borders: {
                        top: { style: "none" },
                        bottom: { style: "none" },
                        left: { style: "none" },
                        right: { style: "none" },
                      },
                      width: { size: 50, type: WidthType.PERCENTAGE }
                    }),

                    // Right image cell (will be populated if available)
                    rightSection ? (
                      (() => {
                        try {
                          const rightImageBuffer = fs.readFileSync(rightSection.imagePath);

                          // Default dimensions - smaller for side-by-side
                          let rightWidth = 250;
                          let rightHeight = 180;

                          try {
                            // Get the image dimensions to maintain aspect ratio
                            const dimensions = sizeOf(Buffer.from(rightImageBuffer));
                            const maxWidth = 250; // Smaller max width for side-by-side

                            if (dimensions && dimensions.width && dimensions.height) {
                              // Maintain aspect ratio
                              const aspectRatio = dimensions.width / dimensions.height;
                              rightWidth = maxWidth;
                              rightHeight = Math.round(rightWidth / aspectRatio);
                            }
                          } catch (error: any) {
                            _context.warn(`Error getting dimensions for image: ${error.message}`);
                          }

                          return new TableCell({
                            children: [
                              new Paragraph({
                                children: [
                                  new ImageRun({
                                    data: rightImageBuffer,
                                    transformation: {
                                      width: rightWidth,
                                      height: rightHeight
                                    },
                                    type: "jpg"
                                  })
                                ],
                                alignment: AlignmentType.CENTER
                              })
                            ],
                            borders: {
                              top: { style: "none" },
                              bottom: { style: "none" },
                              left: { style: "none" },
                              right: { style: "none" },
                            },
                            width: { size: 50, type: WidthType.PERCENTAGE }
                          });
                        } catch (error) {
                          _context.warn(`Error processing right image: ${error}`);
                          return new TableCell({
                            children: [
                              new Paragraph({
                                text: "[Image error]"
                              })
                            ],
                            borders: {
                              top: { style: "none" },
                              bottom: { style: "none" },
                              left: { style: "none" },
                              right: { style: "none" },
                            },
                            width: { size: 50, type: WidthType.PERCENTAGE }
                          });
                        }
                      })()
                    ) : (
                      new TableCell({
                        children: [new Paragraph({})],
                        borders: {
                          top: { style: "none" },
                          bottom: { style: "none" },
                          left: { style: "none" },
                          right: { style: "none" },
                        },
                        width: { size: 50, type: WidthType.PERCENTAGE }
                      })
                    )
                  ]
                })
              ],
              borders: {
                top: { style: "none" },
                bottom: { style: "none" },
                left: { style: "none" },
                right: { style: "none" },
                insideHorizontal: { style: "none" },
                insideVertical: { style: "none" }
              }
            });

            children.push(imageTable);

            // Add comments if available using a table
            if (leftSection.photoData.comment || (rightSection && rightSection.photoData.comment)) {
              const commentTable = new Table({
                width: { size: 100, type: WidthType.PERCENTAGE },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [
                          new Paragraph({
                            children: [
                              new TextRun({
                                text: leftSection.photoData.comment || "",
                                size: 20 // 10pt
                              })
                            ]
                          })
                        ],
                        borders: {
                          top: { style: "none" },
                          bottom: { style: "none" },
                          left: { style: "none" },
                          right: { style: "none" },
                        },
                        width: { size: 50, type: WidthType.PERCENTAGE }
                      }),
                      new TableCell({
                        children: [
                          new Paragraph({
                            children: [
                              new TextRun({
                                text: rightSection && rightSection.photoData.comment ? rightSection.photoData.comment : "",
                                size: 20 // 10pt
                              })
                            ]
                          })
                        ],
                        borders: {
                          top: { style: "none" },
                          bottom: { style: "none" },
                          left: { style: "none" },
                          right: { style: "none" },
                        },
                        width: { size: 50, type: WidthType.PERCENTAGE }
                      })
                    ]
                  })
                ],
                borders: {
                  top: { style: "none" },
                  bottom: { style: "none" },
                  left: { style: "none" },
                  right: { style: "none" },
                  insideHorizontal: { style: "none" },
                  insideVertical: { style: "none" }
                }
              });

              children.push(
                new Paragraph({
                  spacing: {
                    before: 20,
                    after: 0,
                  }
                }),
                commentTable,
                new Paragraph({
                  spacing: {
                    before: 0,
                    after: 40,
                  }
                })
              );
            }

          } catch (error) {
            _context.warn(`Error processing images: ${error}`);
            children.push(
              new Paragraph({
                text: `[Error processing images]`
              })
            );
          }
        }
      }

      // Add hazards and recommendations section
      const hazards = machineSubform.section_40 || [];
      if (hazards.length > 0) {
        children.push(
          new Paragraph({
            text: "Hazards and Recommendations",
            style: "Heading2", // Explicitly set the style to match the heading level
            spacing: {
              before: 200,
              after: 120,
            }
          })
        );

        for (let j = 0; j < hazards.length; j++) {
          const hazard = hazards[j];
          const hazardFields = hazard.fields || {};

          children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `Hazard ${j + 1}: ${hazardFields.field_403 || "Unnamed Hazard"}`,
                  size: 26, // 13pt
                  font: "Calibri Light",
                  color: "2E74B5",
                  bold: true,
                })
              ],
              spacing: {
                before: 160,
                after: 80,
              },
            }),
            new Paragraph({
              text: `Description: ${hazardFields.field_405 || "No description provided"}`
            })
          );

          // Add hazard photos with placeholders
          if (hazardFields.field_404 && hazardFields.field_404.length > 0) {
            // Group hazard photos into pairs for side-by-side display
            const processedHazardPhotos = [];

            // First, collect all valid hazard photos
            for (const photoData of hazardFields.field_404) {
              if (imageMap[photoData.photo]) {
                const imagePath = imageMap[photoData.photo];
                if (fs.existsSync(imagePath)) {
                  processedHazardPhotos.push({
                    photoData,
                    imagePath
                  });
                }
              }
            }

            // Add hazard photos header
            children.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: "Hazard Photos",
                    size: 26, // 13pt
                    font: "Calibri Light",
                    color: "2E74B5",
                    bold: true,
                  })
                ],
                spacing: {
                  before: 160,
                  after: 80,
                },
              })
            );

            // Process hazard photos in pairs
            for (let i = 0; i < processedHazardPhotos.length; i += 2) {
              const leftPhoto = processedHazardPhotos[i];
              const rightPhoto = i + 1 < processedHazardPhotos.length ? processedHazardPhotos[i + 1] : null;

              try {
                const leftImageBuffer = fs.readFileSync(leftPhoto.imagePath);

                // Default dimensions - smaller for side-by-side
                let leftWidth = 250;
                let leftHeight = 180;

                try {
                  // Get the image dimensions to maintain aspect ratio
                  const dimensions = sizeOf(Buffer.from(leftImageBuffer));
                  const maxWidth = 250; // Smaller max width for side-by-side

                  if (dimensions && dimensions.width && dimensions.height) {
                    // Maintain aspect ratio
                    const aspectRatio = dimensions.width / dimensions.height;
                    leftWidth = maxWidth;
                    leftHeight = Math.round(leftWidth / aspectRatio);
                  }
                } catch (error: any) {
                  _context.warn(`Error getting dimensions for hazard image: ${error.message}`);
                }

                // Create a table for the hazard images
                const hazardImageTable = new Table({
                  width: { size: 100, type: WidthType.PERCENTAGE },
                  rows: [
                    new TableRow({
                      children: [
                        new TableCell({
                          children: [
                            new Paragraph({
                              children: [
                                new ImageRun({
                                  data: leftImageBuffer,
                                  transformation: {
                                    width: leftWidth,
                                    height: leftHeight
                                  },
                                  type: "jpg"
                                })
                              ],
                              alignment: AlignmentType.CENTER
                            })
                          ],
                          borders: {
                            top: { style: "none" },
                            bottom: { style: "none" },
                            left: { style: "none" },
                            right: { style: "none" },
                          },
                          width: { size: 50, type: WidthType.PERCENTAGE }
                        }),

                        // Right image cell (will be populated if available)
                        rightPhoto ? (
                          (() => {
                            try {
                              const rightImageBuffer = fs.readFileSync(rightPhoto.imagePath);

                              // Default dimensions - smaller for side-by-side
                              let rightWidth = 250;
                              let rightHeight = 180;

                              try {
                                // Get the image dimensions to maintain aspect ratio
                                const dimensions = sizeOf(Buffer.from(rightImageBuffer));
                                const maxWidth = 250; // Smaller max width for side-by-side

                                if (dimensions && dimensions.width && dimensions.height) {
                                  // Maintain aspect ratio
                                  const aspectRatio = dimensions.width / dimensions.height;
                                  rightWidth = maxWidth;
                                  rightHeight = Math.round(rightWidth / aspectRatio);
                                }
                              } catch (error: any) {
                                _context.warn(`Error getting dimensions for hazard image: ${error.message}`);
                              }

                              return new TableCell({
                                children: [
                                  new Paragraph({
                                    children: [
                                      new ImageRun({
                                        data: rightImageBuffer,
                                        transformation: {
                                          width: rightWidth,
                                          height: rightHeight
                                        },
                                        type: "jpg"
                                      })
                                    ],
                                    alignment: AlignmentType.CENTER
                                  })
                                ],
                                borders: {
                                  top: { style: "none" },
                                  bottom: { style: "none" },
                                  left: { style: "none" },
                                  right: { style: "none" },
                                },
                                width: { size: 50, type: WidthType.PERCENTAGE }
                              });
                            } catch (error) {
                              _context.warn(`Error processing right hazard image: ${error}`);
                              return new TableCell({
                                children: [
                                  new Paragraph({
                                    text: "[Image error]"
                                  })
                                ],
                                borders: {
                                  top: { style: "none" },
                                  bottom: { style: "none" },
                                  left: { style: "none" },
                                  right: { style: "none" },
                                },
                                width: { size: 50, type: WidthType.PERCENTAGE }
                              });
                            }
                          })()
                        ) : (
                          new TableCell({
                            children: [new Paragraph({})],
                            borders: {
                              top: { style: "none" },
                              bottom: { style: "none" },
                              left: { style: "none" },
                              right: { style: "none" },
                            },
                            width: { size: 50, type: WidthType.PERCENTAGE }
                          })
                        )
                      ]
                    })
                  ],
                  borders: {
                    top: { style: "none" },
                    bottom: { style: "none" },
                    left: { style: "none" },
                    right: { style: "none" },
                    insideHorizontal: { style: "none" },
                    insideVertical: { style: "none" }
                  }
                });

                children.push(hazardImageTable);

                // Add comments if available using a table
                if (leftPhoto.photoData.comment || (rightPhoto && rightPhoto.photoData.comment)) {
                  const commentTable = new Table({
                    width: { size: 100, type: WidthType.PERCENTAGE },
                    rows: [
                      new TableRow({
                        children: [
                          new TableCell({
                            children: [
                              new Paragraph({
                                children: [
                                  new TextRun({
                                    text: leftPhoto.photoData.comment || "",
                                    size: 20 // 10pt
                                  })
                                ]
                              })
                            ],
                            borders: {
                              top: { style: "none" },
                              bottom: { style: "none" },
                              left: { style: "none" },
                              right: { style: "none" },
                            },
                            width: { size: 50, type: WidthType.PERCENTAGE }
                          }),
                          new TableCell({
                            children: [
                              new Paragraph({
                                children: [
                                  new TextRun({
                                    text: rightPhoto && rightPhoto.photoData.comment ? rightPhoto.photoData.comment : "",
                                    size: 20 // 10pt
                                  })
                                ]
                              })
                            ],
                            borders: {
                              top: { style: "none" },
                              bottom: { style: "none" },
                              left: { style: "none" },
                              right: { style: "none" },
                            },
                            width: { size: 50, type: WidthType.PERCENTAGE }
                          })
                        ]
                      })
                    ],
                    borders: {
                      top: { style: "none" },
                      bottom: { style: "none" },
                      left: { style: "none" },
                      right: { style: "none" },
                      insideHorizontal: { style: "none" },
                      insideVertical: { style: "none" }
                    }
                  });

                  children.push(
                    new Paragraph({
                      spacing: {
                        before: 20,
                        after: 0,
                      }
                    }),
                    commentTable,
                    new Paragraph({
                      spacing: {
                        before: 0,
                        after: 40,
                      }
                    })
                  );
                }

              } catch (error) {
                _context.warn(`Error processing hazard images: ${error}`);
                children.push(
                  new Paragraph({
                    text: `[Error processing hazard images]`
                  })
                );
              }
            }
          }

          // Add recommended solutions
          if (hazardFields.field_406 && hazardFields.field_406.length > 0) {
            children.push(
              new Paragraph({
                text: "Recommended Solutions:",
                spacing: {
                  before: 160,
                  after: 80,
                }
              })
            );

            for (const solution of hazardFields.field_406) {
              const solutionChildren = formatSolutionWithLinks(solution);
              children.push(
                new Paragraph({
                  children: solutionChildren,
                  spacing: {
                    after: 80,
                  }
                })
              );
            }
          }

          // Add additional notes if available
          if (hazardFields.multiline_5) {
            children.push(
              new Paragraph({
                text: `Additional Notes: ${hazardFields.multiline_5}`
              })
            );
          }
        }
      }

      // Add Solutions section
      const solutionFields = Object.keys(machineSubform).filter(key => key.startsWith('listpicker_'));
      const otherSolutionsText = machineSubform.multiline_4;

      if (solutionFields.length > 0 || otherSolutionsText) {
        children.push(
          new Paragraph({
            text: "Solutions",
            style: "Heading2",
            spacing: {
              before: 200,
              after: 120,
            }
          })
        );

        // Process each solution field
        for (const fieldKey of solutionFields) {
          const solutions = machineSubform[fieldKey];
          if (solutions && Array.isArray(solutions) && solutions.length > 0) {
            // Add solutions from this field
            for (const solutionKey of solutions) {
              // Handle "other" option with custom text
              if (solutionKey === 'other' && otherSolutionsText) {
                const solutionChildren = formatSolutionWithLinks(solutionKey, otherSolutionsText);
                children.push(
                  new Paragraph({
                    children: solutionChildren,
                    spacing: {
                      after: 80,
                    }
                  })
                );
              } else if (solutionKey !== 'other') {
                // Regular solution
                const solutionChildren = formatSolutionWithLinks(solutionKey);
                children.push(
                  new Paragraph({
                    children: solutionChildren,
                    spacing: {
                      after: 80,
                    }
                  })
                );
              }
            }
          }
        }

        // If there's custom text but no "other" option was selected, still show it
        if (otherSolutionsText && !solutionFields.some(fieldKey => {
          const solutions = machineSubform[fieldKey];
          return solutions && Array.isArray(solutions) && solutions.includes('other');
        })) {
          children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `• ${otherSolutionsText}`,
                  size: 22, // 11pt
                })
              ],
              spacing: {
                after: 80,
              }
            })
          );
        }
      }

      // Add page break after each machine except the last one
      if (i < machines.length - 1) {
        children.push(
          new Paragraph({
            children: [new PageBreak()]
          })
        );
      }
    }
  }

  // Create the document with modern fonts and professional styling
  context.log(`Creating final document with ${children.length} elements`);
  return new Document({
    creator: "Odiz Assessment App",
    title: `Machine Guarding Assessment - ${payload.main_form_facility_name || ""}`,
    description: "Generated machine guarding assessment report",
    features: {
      updateFields: true, // Enable field updates for TOC
    },
    compatibility: {
      doNotExpandShiftReturn: true,
    },
    evenAndOddHeaderAndFooters: false, // Simplify header/footer handling
    styles: {
      default: {
        document: {
          run: {
            font: "Calibri",
            size: 24  // 12pt
          },
          paragraph: {
            spacing: {
              line: 276,  // 1.15 line spacing
            },
          },
        },
      },
      paragraphStyles: [
        {
          id: "Normal",
          name: "Normal",
          run: {
            font: "Calibri",
            size: 24,  // 12pt
          },
          paragraph: {
            spacing: {
              line: 276,  // 1.15 line spacing
              before: 0,
              after: 200,  // 10pt spacing after paragraphs
            },
          },
        },
        {
          id: "Title",
          name: "Title",
          basedOn: "Normal",
          next: "Normal",
          run: {
            font: "Calibri Light",
            size: 56,  // 28pt
            bold: false,
            color: "2E74B5",  // Modern blue
          },
          paragraph: {
            spacing: {
              before: 240,
              after: 120,
            },
            alignment: AlignmentType.CENTER,
          },
        },
        {
          id: "Heading1",
          name: "Heading 1",
          basedOn: "Normal",
          next: "Normal",
          quickFormat: true, // Important for TOC recognition
          run: {
            font: "Calibri Light",
            size: 32,  // 16pt
            bold: false,
            color: "2E74B5",  // Modern blue
          },
          paragraph: {
            spacing: {
              before: 240,
              after: 120,
            },
            outlineLevel: 1, // Explicitly set outline level for TOC (1-based)
          },
        },
        {
          id: "Heading2",
          name: "Heading 2",
          basedOn: "Normal",
          next: "Normal",
          quickFormat: true, // Important for TOC recognition
          run: {
            font: "Calibri Light",
            size: 28,  // 14pt
            bold: false,
            color: "2E74B5",  // Modern blue
          },
          paragraph: {
            spacing: {
              before: 240,
              after: 120,
            },
            outlineLevel: 2, // Explicitly set outline level for TOC (1-based)
          },
        },
        {
          id: "Heading3",
          name: "Heading 3",
          basedOn: "Normal",
          next: "Normal",
          quickFormat: true, // Important for TOC recognition
          run: {
            font: "Calibri Light",
            size: 26,  // 13pt
            bold: false,
            color: "2E74B5",  // Modern blue
          },
          paragraph: {
            spacing: {
              before: 240,
              after: 120,
            },
            outlineLevel: 3, // Explicitly set outline level for TOC (1-based)
          },
        },
        {
          id: "TOCHeading",
          name: "TOC Heading",
          basedOn: "Normal",
          next: "Normal",
          run: {
            font: "Calibri",
            bold: true,
            size: 28,  // 14pt
          },
          paragraph: {
            spacing: {
              before: 200,
              after: 100,
            },
          },
        },
        {
          id: "TOCEntry",
          name: "TOC Entry",
          basedOn: "Normal",
          next: "Normal",
          run: {
            font: "Calibri",
            size: 24,  // 12pt
          },
          paragraph: {
            indent: {
              left: 400,
            },
            spacing: {
              before: 40,
              after: 40,
            },
          },
        },
      ],
    },
    sections: [
      {
        properties: {
          page: {
            margin: {
              top: 1440,  // 0.75 inches (increased for header)
              right: 1000,  // 0.5 inches
              bottom: 1440,  // 0.75 inches (increased for footer)
              left: 1000,  // 0.5 inches
            },
          },
          titlePage: true, // Enable different first page
        },
        headers: {
          default: new Header({
            children: [
              new Paragraph({
                children: [
                  new ImageRun({
                    data: fs.readFileSync(path.join(__dirname, "../images/Odiz-Name-Plate-small.png")),
                    transformation: {
                      width: 43, // 0.6 inches at 72 DPI
                      height: 43, // 0.6 inches at 72 DPI
                    },
                    type: "png",
                  }),
                ],
                alignment: AlignmentType.RIGHT,
                spacing: {
                  after: 200,
                },
              }),
            ],
          }),
          first: new Header({
            children: [
              new Paragraph({
                text: "", // Empty header for first page (cover page)
              }),
            ],
          }),
        },
        footers: {
          default: new Footer({
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    children: ["Page ", PageNumber.CURRENT, " of ", PageNumber.TOTAL_PAGES],
                  }),
                  new TextRun({
                    text: "                    ", // Spacing
                  }),
                  new InternalHyperlink({
                    children: [
                      new TextRun({
                        text: "Table of Contents",
                        style: "Hyperlink",
                        size: 20, // 10pt - smaller than page numbers
                      })
                    ],
                    anchor: "toc",
                  })
                ],
                alignment: AlignmentType.CENTER,
              }),
            ],
          }),
          first: new Footer({
            children: [
              new Paragraph({
                text: "", // Empty footer for first page (cover page)
              }),
            ],
          }),
        },
        children
      },
    ],
  });
}

// Helper functions
function formatDate(dateString?: string): string {
  if (!dateString) return "";
  try {
    return new Date(dateString).toLocaleDateString();
  } catch (e) {
    return dateString;
  }
}

function createTableRow(property: string, value: string | number | undefined | null): TableRow {
  // Ensure value is a string
  const valueStr = value !== undefined && value !== null ? String(value) : "Not specified";

  return new TableRow({
    children: [
      new TableCell({
        children: [new Paragraph({
          children: [new TextRun({ text: property, bold: true })]
        })],
        shading: { fill: "F9F9F9" }, // Very light gray background
        margins: {
          top: 120,    // 0.06 inches (6pt)
          bottom: 120, // 0.06 inches (6pt)
          left: 240,   // 0.12 inches (12pt)
          right: 240   // 0.12 inches (12pt)
        }
      }),
      new TableCell({
        children: [new Paragraph({
          text: valueStr
        })],
        margins: {
          top: 120,    // 0.06 inches (6pt)
          bottom: 120, // 0.06 inches (6pt)
          left: 240,   // 0.12 inches (12pt)
          right: 240   // 0.12 inches (12pt)
        }
      })
    ]
  });
}

function formatMachineType(types?: string[]): string {
  if (!types || types.length === 0) return "Unknown";

  const typeMap: Record<string, string> = {
    "general": "General Machine",
    "complex": "Complex Machine System",
    "abb_wheel": "Abrasive Wheel Grinder / Bench or Pedestal Stand",
    "band_saw": "Bandsaw",
    "cnc": "CNC Machine",
    "drill_press": "Drill Press",
    "honing": "Honing Machine",
    "boring": "Horizontal Boring Machine",
    "horizontal_lathe": "Horizontal Lathe",
    "hyd_press": "Hydraulic Press",
    "ironworker": "Ironworker",
    "milling_machine": "Manual Mill",
    "mech_press": "Mechanical Press",
    "pipe_bender": "Pipe Bender",
    "pipe_thread": "Pipe Threading Machine",
    "press_brake": "Press Break",
    "shear": "Shear",
    "surf_grinder": "Surface Grinder",
    "vtl": "Vertical Turret Lathe",
    "other": "Other"
  };

  return types.map(type => typeMap[type] || type).join(", ");
}

function formatAssessmentLevel(levels?: string[]): string {
  if (!levels || levels.length === 0) return "Unknown";

  const levelMap: Record<string, string> = {
    "osha": "OSHA Standards",
    "ansi-iso": "ANSI/ISO Standards",
    "none": "No Specific Standard"
  };

  return levels.map(level => levelMap[level] || level).join(", ");
}

function formatYesNo(value: any): string {
  if (value === 1 || value === "1" || value === true || value === "true" || value === "Yes") {
    return "Yes";
  } else if (value === 0 || value === "0" || value === false || value === "false" || value === "No") {
    return "No";
  } else {
    return "Unknown";
  }
}

// Helper function to create assessment question rows
function createAssessmentRow(question: string, answer: any): TableRow {
  const answerText = formatYesNo(answer);
  const backgroundColor = answerText === "Yes" ? "E6F4EA" : // Light green for Yes
                         answerText === "No" ? "FCE8E6" :   // Light red for No
                         "F8F9FA";                          // Light gray for Unknown

  return new TableRow({
    children: [
      new TableCell({
        children: [new Paragraph({
          children: [new TextRun({ text: question })]
        })],
        margins: {
          top: 120,
          bottom: 120,
          left: 240,
          right: 240
        }
      }),
      new TableCell({
        children: [new Paragraph({
          children: [new TextRun({
            text: answerText,
            bold: true
          })]
        })],
        shading: { fill: backgroundColor },
        margins: {
          top: 120,
          bottom: 120,
          left: 240,
          right: 240
        }
      })
    ]
  });
}

app.storageQueue("processSummaryDocx", {
  queueName: "summary-tasks-docx",
  connection: "AzureWebJobsStorage",
  handler: processSummaryDocx,
});
