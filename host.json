{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request", "maxTelemetryItemsPerSecond": 20}}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "extensions": {"http": {"routePrefix": "api", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true, "customHeaders": {"enableRawBody": true}}, "queues": {"maxPollingInterval": "00:00:02", "batchSize": 16, "maxDequeueCount": 5, "newBatchThreshold": 8}}, "functionTimeout": "00:10:00"}