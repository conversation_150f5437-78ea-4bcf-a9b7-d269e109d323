{"name": "odiz-assessment-functions", "version": "1.0.0", "description": "", "main": "dist/src/functions/*.js", "scripts": {"build": "tsc && npm run copy-assets", "copy-assets": "powershell -Command \"if (!(Test-Path 'dist/src/images')) { New-Item -ItemType Directory -Path 'dist/src/images' -Force }; Copy-Item 'src/images/*' 'dist/src/images/' -Force; Copy-Item 'solution-links.json' 'dist/' -Force; Copy-Item 'form-def.json' 'dist/' -Force\"", "watch": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run clean && npm run build", "start": "func start", "test": "echo \"No tests yet...\""}, "dependencies": {"@azure/functions": "^4.7.0", "@azure/identity": "^4.0.1", "@azure/storage-blob": "^12.27.0", "@azure/storage-queue": "^12.26.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@types/busboy": "^1.5.4", "@types/html-to-docx": "^1.8.0", "axios": "^1.9.0", "busboy": "^1.6.0", "docx": "^9.5.0", "docxtemplater": "^3.61.2", "formidable": "^3.5.4", "html-to-docx": "^1.8.0", "image-size": "^2.0.2", "jszip": "^3.10.1", "mssql": "^11.0.1", "node-fetch": "^3.3.2", "parse-multipart": "^1.0.4", "pizzip": "^3.1.8", "xlsx": "^0.18.5"}, "devDependencies": {"@types/formidable": "^3.4.5", "@types/mssql": "^9.1.7", "@types/node": "18.x", "@types/node-fetch": "^2.6.12", "@types/pizzip": "^3.0.5", "@types/uuid": "^10.0.0", "azure-functions-core-tools": "^4.x", "rimraf": "^5.0.0", "typescript": "^4.0.0"}}