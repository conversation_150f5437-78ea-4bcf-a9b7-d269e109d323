import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";

/**
 * A simple test function to verify that the Function App is working correctly
 */
export async function testFunction(
  request: HttpRequest,
  context: InvocationContext
): Promise<HttpResponseInit> {
  context.log("Test function triggered");
  
  return {
    status: 200,
    body: JSON.stringify({
      message: "Test function is working correctly",
      timestamp: new Date().toISOString(),
      environment: {
        azureWebJobsStorage: !!process.env.AzureWebJobsStorage,
        sqlConnStr: !!process.env.SQL_CONN_STR,
        fastfieldDevApiKey: !!process.env.FASTFIELD_DEV_API_KEY
      }
    })
  };
}

app.http("testFunction", {
  methods: ["GET", "POST"],
  authLevel: "anonymous", // Make it anonymous for easy testing
  handler: testFunction,
});
