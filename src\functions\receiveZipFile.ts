import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { BlobServiceClient } from "@azure/storage-blob";
import { QueueServiceClient } from "@azure/storage-queue";
import <PERSON><PERSON>, { BusboyConfig } from "busboy";

/**
 * Helper function to process an uploaded ZIP file
 * This function queues the ZIP for processing and returns a response
 */
async function processUploadedZip(blobName: string, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Queue a message for processing
    const queueClient = QueueServiceClient
      .fromConnectionString(process.env.AzureWebJobsStorage!)
      .getQueueClient("zip-processing");

    await queueClient.createIfNotExists();

    // Create a message with the blob name
    const message = {
      blobName: blobName,
      timestamp: new Date().toISOString()
    };

    await queueClient.sendMessage(Buffer.from(JSON.stringify(message)).toString("base64"));
    context.log("Queued ZIP file for processing");

    // Return success response
    return {
      status: 202, // Accepted
      body: JSON.stringify({
        message: "ZIP file received and queued for processing",
        blobName: blobName
      })
    };
  } catch (error: any) {
    context.error("Error queuing ZIP for processing", error);
    return {
      status: 500,
      body: `Failed to queue ZIP file for processing: ${error.message}`
    };
  }
}

export async function receiveZipFile(
  request: HttpRequest,
  context: InvocationContext
): Promise<HttpResponseInit> {
  try {
    // Add request logging
    context.log("Processing ZIP file upload request", {
      contentType: request.headers.get("content-type"),
      contentLength: request.headers.get("content-length"),
      requestId: context.invocationId
    });

    // 0) Secret check
    const key = request.headers.get("x-api-key") || request.headers.get("x-hook-secret");
    if (key !== process.env.FASTFIELD_DEV_API_KEY) {
      return { status: 401, body: "Invalid hook secret" };
    }

    // 1) Pull out a Buffer for the ZIP with progress logging
    const contentType = request.headers.get("content-type") || "";
    let zipBuffer: Buffer | null;

    if (contentType.startsWith("multipart/form-data")) {
      context.log("Processing multipart form data");
      const headersObject = Object.fromEntries(request.headers.entries());

      // Add more detailed logging for debugging
      context.log("Headers for Busboy:", JSON.stringify(headersObject));

      // Configure Busboy with limits to prevent excessive memory usage
      const bb = Busboy({
        headers: headersObject,
        limits: {
          fileSize: 100 * 1024 * 1024, // 100MB max file size
          files: 1 // Only expect one file
        }
      } as BusboyConfig);

      zipBuffer = await new Promise<Buffer>((resolve, reject) => {
        let buf: Buffer[] = [];
        let found = false;
        let totalSize = 0;
        let fileFieldName = '';

        // Define interfaces for better type safety
        interface FileStreamEvents {
          on(event: 'data', listener: (chunk: Buffer) => void): void;
          on(event: 'end', listener: () => void): void;
        }

        interface BusboyFileStream extends FileStreamEvents {
          readonly filename: string;
          readonly encoding: string;
          readonly mimetype: string;
        }

        // Log all field names to help debug
        bb.on("field", (fieldname: string, val: string) => {
          context.log(`Form field: ${fieldname} = ${val}`);
        });

        bb.on("file", (fieldname: string, stream: BusboyFileStream, filename: string, _encoding: string, mimetype: string) => {
          context.log(`Processing file: ${fieldname}, filename: ${filename}, mimetype: ${mimetype}`);
          fileFieldName = fieldname;

          // Accept any file from the form, not just application/zip
          // We'll validate it's a ZIP later
          found = true;

          stream.on("data", (chunk: Buffer) => {
            buf.push(chunk);
            totalSize += chunk.length;
            // Log less frequently to reduce overhead
            if (totalSize % (1024 * 1024) === 0) {
              context.log(`Reading file: ${Math.round(totalSize / (1024 * 1024))}MB`);
            }
          });

          stream.on("end", () => {
            context.log(`Completed reading file: ${totalSize} bytes`);
          });
        });

        bb.on("finish", () => {
          context.log("Busboy processing finished");
          if (!found) {
            reject(new Error("No file part found in multipart upload. Expected a file field."));
          } else {
            const buffer = Buffer.concat(buf);
            context.log(`File ${fileFieldName} received: ${buffer.length} bytes`);
            resolve(buffer);
          }
        });

        bb.on("error", (err) => {
          context.error("Busboy error", err);
          reject(err);
        });

        // Stream the body
        if (!request.body) {
          reject(new Error("Request body is null"));
          return;
        }

        const reader = request.body.getReader();
        const pump = async () => {
          try {
            while (true) {
              const {done, value} = await reader.read();
              if (done) {
                bb.end();
                break;
              }
              bb.write(Buffer.from(value));
            }
          } catch (err) {
            context.error("Stream reading error", err);
            reject(err);
          }
        };
        pump();
      });
    } else if (contentType === "application/zip") {
      // raw‑body style
      const arrayBuffer = await request.arrayBuffer();
      zipBuffer = Buffer.from(arrayBuffer);
      if (!Buffer.isBuffer(zipBuffer)) {
        return { status: 400, body: "Expected binary ZIP in request body" };
      }
    } else {
      return { status: 400, body: "Unsupported Content-Type" };
    }

    // 1.5) Validate that the buffer is a valid ZIP file
    try {
      // Check for ZIP file signature (first 4 bytes should be 0x50 0x4B 0x03 0x04)
      if (zipBuffer.length < 4 ||
          zipBuffer[0] !== 0x50 ||
          zipBuffer[1] !== 0x4B ||
          zipBuffer[2] !== 0x03 ||
          zipBuffer[3] !== 0x04) {
        context.error("Invalid ZIP file signature");
        return {
          status: 400,
          body: "The uploaded file does not appear to be a valid ZIP file"
        };
      }

      context.log("ZIP file signature validated");
    } catch (error) {
      context.error("Error validating ZIP file", error);
      return {
        status: 400,
        body: "Failed to validate the uploaded file as a ZIP"
      };
    }

    // 2) Upload the ZIP file to blob storage
    context.log("Starting blob storage upload");
    const blobSvc = BlobServiceClient.fromConnectionString(process.env.AzureWebJobsStorage!);
    const container = blobSvc.getContainerClient("zip-submissions");

    try {
      await container.createIfNotExists();
      context.log("Container exists or was created");

      // Generate a unique blob name using timestamp and random string
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const randomId = Math.random().toString(36).substring(2, 10);
      const blobName = `submission-${timestamp}-${randomId}.zip`;

      // Upload the ZIP file
      const blockBlobClient = container.getBlockBlobClient(blobName);

      context.log(`Starting upload of ${zipBuffer.length} bytes to blob storage`);
      await blockBlobClient.uploadData(zipBuffer, {
        blobHTTPHeaders: { blobContentType: 'application/zip' }
      });

      context.log(`Uploaded ZIP file to blob storage: ${blobName}`);

      // Clear the buffer to free memory
      zipBuffer = null;

      return await processUploadedZip(blobName, context);
    } catch (error: any) {
      context.error("Error uploading to blob storage", error);
      return {
        status: 500,
        body: `Failed to upload ZIP file to storage: ${error.message}`
      };
    }
  } catch (error: any) {
    context.error("Function failed", {
      error: error.message,
      stack: error.stack,
      invocationId: context.invocationId
    });
    return {
      status: 500,
      body: `Processing failed: ${error.message}`
    };
  }
}

app.http("receiveZipFile", {
  methods: ["POST"],
  authLevel: "function",
  handler: receiveZipFile,
});
