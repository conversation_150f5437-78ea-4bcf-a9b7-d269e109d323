import { app, In<PERSON><PERSON><PERSON><PERSON><PERSON>, St<PERSON><PERSON>ueue<PERSON><PERSON><PERSON> } from "@azure/functions";
import * as sql from "mssql";
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, BorderStyle, WidthType, AlignmentType, <PERSON>Run, <PERSON><PERSON>, <PERSON>er, PageBreak, TableOfContents, Bookmark, ShadingType } from "docx";
import { BlobServiceClient } from "@azure/storage-blob";
import { ClientSecretCredential } from "@azure/identity";
import { Client } from "@microsoft/microsoft-graph-client";

// Define interfaces for better type safety
interface FormPayload {
  formId?: number;
  formName?: string;
  submissionId?: string;
  accountId?: number;
  main_form_facility_name?: string;
  main_form_first_name?: string;
  main_form_last_name?: string;
  main_form_email_address?: string;
  main_form_phone_number?: string;
  main_form_date?: string;
  main_form_nameplate?: string;
  formMetaData?: any;
  section_2?: MachineSection[];
  [key: string]: any;
}

interface MachineSection {
  sectionCounter: number;
  appended?: string;
  fields: {
    main_form_machine_title?: string;
    main_form_machine_type?: string[];
    main_form_general_machine_subform?: MachineSubform[];
    [key: string]: any;
  };
}

interface MachineSubform {
  reserved_instanceGuid?: string;
  general_machine_type?: string[];
  assessment_level?: string[];
  machine_name_and_id?: string;
  location_of_machine?: string;
  department?: string;
  machine_manufacturer?: string;
  machine_model_number?: string;
  machine_serial_number?: string;
  operator_names?: string;
  machine_description?: string;
  is_machine_in_operation?: number;
  was_machine_observed_during_observation?: number;
  known_injuries_associated_with_machine?: number;
  photo_of_machine_nameplate?: PhotoWithComment[];
  photo_of_machine_id_num?: PhotoWithComment[];
  photo_of_machine_front?: PhotoWithComment[];
  photo_of_machine_right?: PhotoWithComment[];
  photo_of_machine_back?: PhotoWithComment[];
  photo_of_machine_left?: PhotoWithComment[];
  section_40?: HazardSection[];
  [key: string]: any;
}

interface PhotoWithComment {
  comment: string;
  photo: string;
}

interface HazardSection {
  sectionCounter: number;
  appended?: string;
  fields: {
    field_403?: string; // Hazard name
    field_404?: PhotoWithComment[]; // Hazard photo
    field_405?: string; // Hazard description
    field_406?: string[]; // Recommended solutions
    multiline_5?: string; // Additional notes
    [key: string]: any;
  };
}

interface SubmissionData {
  id: number;
  payload: FormPayload;
  attachmentUrls?: Record<string, string>;
  zipBlobName?: string;
  submissionDate?: Date;
}

export const processSummary: StorageQueueHandler = async (
  rawMessage: unknown,
  context: InvocationContext
): Promise<void> => {
  try {
    // 1) Decode the submission ID
    const msg = String(rawMessage);
    const submissionId = /^\d+$/.test(msg)
      ? parseInt(msg, 10)
      : parseInt(Buffer.from(msg, "base64").toString("utf8"), 10);
    context.log("Processing submission ID:", submissionId);

    // 2) Load the submission data from SQL
    let pool: sql.ConnectionPool;
    try {
      pool = await sql.connect(process.env.SQL_CONN_STR!);
      context.log("Connected to SQL database");
    } catch (error: any) {
      context.error("Failed to connect to SQL database", error);
      throw new Error(`SQL connection failed: ${error.message}`);
    }

    let submission: SubmissionData;
    try {
      const { recordset } = await pool.request()
        .input('id', sql.Int, submissionId)
        .query(`
          SELECT Id, Payload, AttachmentUrls, ZipBlobName, GETDATE() as SubmissionDate
          FROM dbo.FastFieldSubmissions
          WHERE Id = @id
        `);

      if (!recordset || recordset.length === 0) {
        throw new Error(`Submission with ID ${submissionId} not found`);
      }

      submission = {
        id: recordset[0].Id,
        payload: JSON.parse(recordset[0].Payload),
        attachmentUrls: recordset[0].AttachmentUrls ? JSON.parse(recordset[0].AttachmentUrls) : {},
        zipBlobName: recordset[0].ZipBlobName,
        submissionDate: recordset[0].SubmissionDate
      };

      context.log(`Loaded submission data for ID ${submissionId}`);
    } catch (error: any) {
      context.error("Failed to load submission data", error);
      throw new Error(`Failed to load submission data: ${error.message}`);
    }

    // 3) Create a professional document using docx library
    const doc = await createProfessionalDocument(
      submission,
      context
    );

    // 4) Generate the document buffer
    const buffer = await Packer.toBuffer(doc);

    // 5) Authenticate to Microsoft Graph and upload
    const credential = new ClientSecretCredential(
      process.env.AZURE_TENANT_ID!,
      process.env.AZURE_CLIENT_ID!,
      process.env.AZURE_CLIENT_SECRET!
    );
    const graph = Client.initWithMiddleware({
      authProvider: {
        getAccessToken: async () =>
          (await credential.getToken(".default"))?.token || "",
      },
    });

    // 6) Pick a non‑conflicting filename
    const siteId = process.env.SHAREPOINT_SITE_ID!;
    let attempt = 0;
    let uploadPath: string;
    while (true) {
      const suffix = attempt === 0 ? "" : `-${attempt}`;
      uploadPath = `Summaries/${submissionId}${suffix}.docx`;
      try {
        await graph.api(`/sites/${siteId}/drive/root:/${uploadPath}`).get();
        attempt++;
      } catch (e: any) {
        if (e.statusCode === 404) break;
        throw e;
      }
    }

    // 7) Final upload with proper content type and permissions
    try {
      // Upload with proper content type
      await graph
        .api(`/sites/${siteId}/drive/root:/${uploadPath}:/content`)
        .header("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
        .put(buffer);

      // Get the item ID to update permissions
      const fileInfo = await graph
        .api(`/sites/${siteId}/drive/root:/${uploadPath}`)
        .get();

      context.log(`File uploaded successfully. Item ID: ${fileInfo.id}`);

      // Update file permissions to allow editing
      if (fileInfo.id) {
        try {
          // Set the file to be editable
          await graph
            .api(`/sites/${siteId}/drive/items/${fileInfo.id}`)
            .patch({
              "@microsoft.graph.conflictBehavior": "replace",
              file: {
                mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              }
            });

          context.log("File permissions updated to allow editing");
        } catch (permError: any) {
          context.warn(`Could not update file permissions: ${permError.message}`);
        }
      }

      context.log(`✔️ Uploaded submission doc as ${uploadPath}`);
    } catch (uploadError: any) {
      context.error(`Error uploading document: ${uploadError.message}`);
      throw uploadError;
    }
  } catch (error: any) {
    context.error("Function failed", {
      error: error.message,
      stack: error.stack,
      functionName: "processSummary"
    });
    throw error; // Rethrow to trigger retry
  }
};

/**
 * Creates a professional document using the docx library
 */
async function createProfessionalDocument(
  submission: SubmissionData,
  _context: InvocationContext  // Prefixed with underscore to indicate it's intentionally unused
): Promise<Document> {
  const { payload, attachmentUrls = {} } = submission;
  const machines = payload.section_2 || [];

  // Create document sections for table of contents and content
  const children: any[] = [];
  const bookmarks: Record<string, string> = {};

  // Add cover page
  children.push(
    new Paragraph({
      text: "Machine Guarding Assessment Report",
      heading: HeadingLevel.TITLE,
      alignment: AlignmentType.CENTER,
      spacing: { before: 300, after: 100 }
    }),
    new Paragraph({
      text: payload.formName || "Odiz Machine Guarding Assessment",
      alignment: AlignmentType.CENTER,
      spacing: { after: 200 }
    }),
    new Paragraph({
      text: `Facility: ${payload.main_form_facility_name || ""}`,
      alignment: AlignmentType.CENTER,
      spacing: { after: 50 }
    }),
    new Paragraph({
      text: `Assessment Date: ${formatDate(payload.main_form_date)}`,
      alignment: AlignmentType.CENTER,
      spacing: { after: 50 }
    }),
    new Paragraph({
      text: `Assessor: ${payload.main_form_first_name || ""} ${payload.main_form_last_name || ""}`,
      alignment: AlignmentType.CENTER,
      spacing: { after: 50 }
    }),
    new Paragraph({
      text: `Contact: ${payload.main_form_email_address || ""} | ${payload.main_form_phone_number || ""}`,
      alignment: AlignmentType.CENTER,
      spacing: { after: 200 }
    }),
    new Paragraph({
      text: `Submission ID: ${submission.id}`,
      alignment: AlignmentType.CENTER,
      spacing: { after: 50 }
    }),
    new Paragraph({
      text: `Report Generated: ${new Date().toLocaleDateString()}`,
      alignment: AlignmentType.CENTER,
      spacing: { after: 300 }
    }),
    new Paragraph({
      children: [new PageBreak()]
    })
  );

  // Add table of contents
  children.push(
    new Paragraph({
      text: "Table of Contents",
      heading: HeadingLevel.HEADING_1,
      spacing: { after: 200 }
    }),
    new TableOfContents("Table of Contents", {
      hyperlink: true,
      headingStyleRange: "1-3"
    }),
    new Paragraph({
      children: [new PageBreak()]
    })
  );

  // Add assessment summary section
  children.push(
    new Paragraph({
      text: "Assessment Summary",
      heading: HeadingLevel.HEADING_1,
      spacing: { before: 200, after: 100 }
    }),
    new Paragraph({
      text: "This report provides a detailed assessment of machine guarding and safety measures for the equipment evaluated. Each machine has been assessed for compliance with applicable safety standards and recommendations for improvements have been provided where necessary.",
      spacing: { after: 100 }
    }),
    new Table({
      width: { size: 100, type: WidthType.PERCENTAGE },
      rows: [
        new TableRow({
          tableHeader: true,
          children: [
            new TableCell({
              children: [new Paragraph("Assessment Details")],
              shading: { fill: "F2F2F2", type: ShadingType.SOLID },
              columnSpan: 2
            })
          ]
        }),
        new TableRow({
          children: [
            new TableCell({ children: [new Paragraph("Facility Name")] }),
            new TableCell({ children: [new Paragraph(payload.main_form_facility_name || "")] })
          ]
        }),
        new TableRow({
          children: [
            new TableCell({ children: [new Paragraph("Assessment Date")] }),
            new TableCell({ children: [new Paragraph(formatDate(payload.main_form_date))] })
          ]
        }),
        new TableRow({
          children: [
            new TableCell({ children: [new Paragraph("Assessor")] }),
            new TableCell({ children: [new Paragraph(`${payload.main_form_first_name || ""} ${payload.main_form_last_name || ""}`)] })
          ]
        }),
        new TableRow({
          children: [
            new TableCell({ children: [new Paragraph("Contact Information")] }),
            new TableCell({ children: [new Paragraph(`${payload.main_form_email_address || ""} | ${payload.main_form_phone_number || ""}`)] })
          ]
        }),
        new TableRow({
          children: [
            new TableCell({ children: [new Paragraph("Number of Machines Assessed")] }),
            new TableCell({ children: [new Paragraph(`${machines.length}`)] })
          ]
        })
      ]
    }),
    new Paragraph({
      children: [new PageBreak()]
    })
  );

  // Add machine list section
  if (machines.length > 0) {
    children.push(
      new Paragraph({
        text: "Machines Assessed",
        heading: HeadingLevel.HEADING_1,
        spacing: { before: 200, after: 100 }
      }),
      new Table({
        width: { size: 100, type: WidthType.PERCENTAGE },
        rows: [
          new TableRow({
            tableHeader: true,
            children: [
              new TableCell({
                children: [new Paragraph("Machine Name")],
                shading: { fill: "F2F2F2", type: ShadingType.SOLID }
              }),
              new TableCell({
                children: [new Paragraph("Machine Type")],
                shading: { fill: "F2F2F2", type: ShadingType.SOLID }
              }),
              new TableCell({
                children: [new Paragraph("Location")],
                shading: { fill: "F2F2F2", type: ShadingType.SOLID }
              }),
              new TableCell({
                children: [new Paragraph("Department")],
                shading: { fill: "F2F2F2", type: ShadingType.SOLID }
              })
            ]
          }),
          ...machines.map(machine => {
            const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};
            return new TableRow({
              children: [
                new TableCell({
                  children: [new Paragraph(machine.fields.main_form_machine_title || "Unnamed Machine")]
                }),
                new TableCell({
                  children: [new Paragraph(formatMachineType(machineSubform.general_machine_type))]
                }),
                new TableCell({
                  children: [new Paragraph(machineSubform.location_of_machine || "")]
                }),
                new TableCell({
                  children: [new Paragraph(machineSubform.department || "")]
                })
              ]
            });
          })
        ]
      }),
      new Paragraph({
        children: [new PageBreak()]
      })
    );
  }

  // Process each machine
  for (let i = 0; i < machines.length; i++) {
    const machine = machines[i];
    const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};
    const machineNumber = i + 1;
    const bookmarkId = `machine_${machineNumber}`;
    const machineTitle = machine.fields.main_form_machine_title || `Machine ${machineNumber}`;

    bookmarks[bookmarkId] = machineTitle;

    // Add machine heading with bookmark
    children.push(
      new Bookmark({
        id: bookmarkId,
        children: [
          new Paragraph({
            text: machineTitle,
            heading: HeadingLevel.HEADING_1,
            spacing: { before: 200, after: 100 }
          })
        ]
      })
    );

    // Add machine details section
    children.push(
      new Paragraph({
        text: "Machine Details",
        heading: HeadingLevel.HEADING_2,
        spacing: { before: 100, after: 50 }
      }),
      new Table({
        width: { size: 100, type: WidthType.PERCENTAGE },
        rows: [
          new TableRow({
            tableHeader: true,
            children: [
              new TableCell({
                children: [new Paragraph("Property")],
                shading: { fill: "F2F2F2", type: ShadingType.SOLID }
              }),
              new TableCell({
                children: [new Paragraph("Value")],
                shading: { fill: "F2F2F2", type: ShadingType.SOLID }
              })
            ]
          }),
          createTableRow("Machine Type", formatMachineType(machineSubform.general_machine_type)),
          createTableRow("Assessment Level", formatAssessmentLevel(machineSubform.assessment_level)),
          createTableRow("Machine ID", machineSubform.machine_name_and_id || ""),
          createTableRow("Location", machineSubform.location_of_machine || ""),
          createTableRow("Department", machineSubform.department || ""),
          createTableRow("Manufacturer", machineSubform.machine_manufacturer || ""),
          createTableRow("Model Number", machineSubform.machine_model_number || ""),
          createTableRow("Serial Number", machineSubform.machine_serial_number || ""),
          createTableRow("Operator Names", machineSubform.operator_names || ""),
          createTableRow("Machine Description", machineSubform.machine_description || ""),
          createTableRow("Machine in Operation", formatYesNo(machineSubform.is_machine_in_operation)),
          createTableRow("Machine Observed During Assessment", formatYesNo(machineSubform.was_machine_observed_during_observation)),
          createTableRow("Known Injuries Associated with Machine", formatYesNo(machineSubform.known_injuries_associated_with_machine))
        ]
      })
    );

    // Add machine photos section
    const photoSections = [
      { title: "Machine Nameplate", photos: machineSubform.photo_of_machine_nameplate },
      { title: "Machine ID", photos: machineSubform.photo_of_machine_id_num },
      { title: "Machine Front View", photos: machineSubform.photo_of_machine_front },
      { title: "Machine Right View", photos: machineSubform.photo_of_machine_right },
      { title: "Machine Back View", photos: machineSubform.photo_of_machine_back },
      { title: "Machine Left View", photos: machineSubform.photo_of_machine_left }
    ];

    if (photoSections.some(section => section.photos && section.photos.length > 0)) {
      children.push(
        new Paragraph({
          text: "Machine Photos",
          heading: HeadingLevel.HEADING_2,
          spacing: { before: 100, after: 50 }
        })
      );

      for (const section of photoSections) {
        if (section.photos && section.photos.length > 0) {
          for (const photoData of section.photos) {
            const photoUrl = attachmentUrls[photoData.photo];
            if (photoUrl) {
              children.push(
                new Paragraph({
                  text: section.title,
                  heading: HeadingLevel.HEADING_3,
                  spacing: { before: 50, after: 20 }
                }),
                new Paragraph({
                  text: `Photo URL: ${photoUrl}`,
                  spacing: { after: 20 }
                })
              );

              if (photoData.comment) {
                children.push(
                  new Paragraph({
                    text: `Comment: ${photoData.comment}`,
                    spacing: { after: 20 }
                  })
                );
              }
            }
          }
        }
      }
    }

    // Add hazards and recommendations section
    const hazards = machineSubform.section_40 || [];
    if (hazards.length > 0) {
      children.push(
        new Paragraph({
          text: "Hazards and Recommendations",
          heading: HeadingLevel.HEADING_2,
          spacing: { before: 100, after: 50 }
        })
      );

      for (let j = 0; j < hazards.length; j++) {
        const hazard = hazards[j];
        const hazardFields = hazard.fields || {};

        children.push(
          new Paragraph({
            text: `Hazard ${j + 1}: ${hazardFields.field_403 || "Unnamed Hazard"}`,
            heading: HeadingLevel.HEADING_3,
            spacing: { before: 50, after: 20 }
          }),
          new Paragraph({
            text: `Description: ${hazardFields.field_405 || "No description provided"}`,
            spacing: { after: 20 }
          })
        );

        if (hazardFields.field_406 && hazardFields.field_406.length > 0) {
          children.push(
            new Paragraph({
              text: "Recommended Solutions:",
              spacing: { after: 10 }
            })
          );

          for (const solution of hazardFields.field_406) {
            children.push(
              new Paragraph({
                text: `• ${solution}`,
                spacing: { after: 5 }
              })
            );
          }
        }

        if (hazardFields.multiline_5) {
          children.push(
            new Paragraph({
              text: `Additional Notes: ${hazardFields.multiline_5}`,
              spacing: { after: 20 }
            })
          );
        }

        // Add hazard photos
        if (hazardFields.field_404 && hazardFields.field_404.length > 0) {
          for (const photoData of hazardFields.field_404) {
            const photoUrl = attachmentUrls[photoData.photo];
            if (photoUrl) {
              children.push(
                new Paragraph({
                  text: "Hazard Photo",
                  spacing: { before: 20, after: 10 }
                }),
                new Paragraph({
                  text: `Photo URL: ${photoUrl}`,
                  spacing: { after: 10 }
                })
              );

              if (photoData.comment) {
                children.push(
                  new Paragraph({
                    text: `Comment: ${photoData.comment}`,
                    spacing: { after: 20 }
                  })
                );
              }
            }
          }
        }
      }
    }

    // Add page break after each machine except the last one
    if (i < machines.length - 1) {
      children.push(
        new Paragraph({
          children: [new PageBreak()]
        })
      );
    }
  }

  // Create the document
  return new Document({
    creator: "Odiz Assessment App",
    title: `Machine Guarding Assessment - ${payload.main_form_facility_name || ""}`,
    description: "Generated machine guarding assessment report",
    sections: [
      {
        properties: {
          page: {
            margin: {
              top: 1000,
              right: 1000,
              bottom: 1000,
              left: 1000,
            },
          },
        },
        headers: {
          default: new Header({
            children: [
              new Paragraph({
                text: `Machine Guarding Assessment - ${payload.main_form_facility_name || ""}`,
                alignment: AlignmentType.RIGHT,
              }),
            ],
          }),
        },
        footers: {
          default: new Footer({
            children: [
              new Paragraph({
                alignment: AlignmentType.CENTER,
                children: [
                  new TextRun("Page "),
                  new TextRun({
                    children: ["PAGE"],
                    style: "PageNumber"
                  }),
                ],
              }),
            ],
          }),
        },
        children
      },
    ],
  });
}

// Helper functions
function createTableRow(property: string, value: string): TableRow {
  return new TableRow({
    children: [
      new TableCell({ children: [new Paragraph(property)] }),
      new TableCell({ children: [new Paragraph(value)] })
    ]
  });
}

function formatDate(dateString?: string): string {
  if (!dateString) return "";
  try {
    return new Date(dateString).toLocaleDateString();
  } catch (e) {
    return dateString;
  }
}

function formatMachineType(types?: string[]): string {
  if (!types || types.length === 0) return "Unknown";

  const typeMap: Record<string, string> = {
    "general": "General Machine",
    "complex": "Complex Machine System",
    "abb_wheel": "Abrasive Wheel Grinder / Bench or Pedestal Stand",
    "band_saw": "Bandsaw",
    "cnc": "CNC Machine",
    "drill_press": "Drill Press",
    "honing": "Honing Machine",
    "boring": "Horizontal Boring Machine",
    "horizontal_lathe": "Horizontal Lathe",
    "hyd_press": "Hydraulic Press",
    "ironworker": "Ironworker",
    "milling_machine": "Manual Mill",
    "mech_press": "Mechanical Press",
    "pipe_bender": "Pipe Bender",
    "pipe_thread": "Pipe Threading Machine",
    "press_brake": "Press Break",
    "shear": "Shear",
    "surf_grinder": "Surface Grinder",
    "vtl": "Vertical Turret Lathe",
    "other": "Other"
  };

  return types.map(type => typeMap[type] || type).join(", ");
}

function formatAssessmentLevel(levels?: string[]): string {
  if (!levels || levels.length === 0) return "Unknown";

  const levelMap: Record<string, string> = {
    "osha": "OSHA Standards",
    "ansi-iso": "ANSI/ISO Standards",
    "none": "No Specific Standard"
  };

  return levels.map(level => levelMap[level] || level).join(", ");
}

function formatYesNo(value?: number): string {
  if (value === undefined || value === null) return "Unknown";
  return value === 0 ? "Yes" : value === 1 ? "No" : "N/A";
}

app.storageQueue("processSummary", {
  queueName: "summary-tasks",
  connection: "AzureWebJobsStorage",
  handler: processSummary,
});
