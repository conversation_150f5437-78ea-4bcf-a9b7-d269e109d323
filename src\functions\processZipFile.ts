import { app, InvocationContext, StorageQueue<PERSON><PERSON><PERSON> } from "@azure/functions";
import * as sql from "mssql";
import J<PERSON><PERSON><PERSON> from "jszip";
import { BlobServiceClient } from "@azure/storage-blob";
import { QueueServiceClient } from "@azure/storage-queue";

// SQL connection pool - initialize lazily
let SQL_POOL: sql.ConnectionPool | null = null;
let poolConnect: Promise<sql.ConnectionPool> | null = null;

// Helper function to get SQL connection
async function getSqlConnection(context: InvocationContext): Promise<sql.ConnectionPool> {
  try {
    if (!SQL_POOL) {
      context.log("Initializing SQL connection pool");
      SQL_POOL = new sql.ConnectionPool(process.env.SQL_CONN_STR!);
    }

    if (!poolConnect) {
      context.log("Connecting to SQL database");
      poolConnect = SQL_POOL.connect();
    }

    const pool = await poolConnect;
    context.log("SQL connection established");
    return pool;
  } catch (error: any) {
    context.error("Failed to connect to SQL database", {
      error: error.message,
      stack: error.stack
    });
    throw new Error(`SQL connection failed: ${error.message}`);
  }
}

export const processZipFile: StorageQueueHandler = async (
  rawMessage: unknown,
  context: InvocationContext
): Promise<void> => {
  try {
    // Log at the very beginning to see if the function is being triggered
    console.log("processZipFile function triggered - console.log");
    context.log("processZipFile function triggered - context.log");

    // Log environment variables (without sensitive information)
    context.log("Environment variables check:");
    context.log(`AzureWebJobsStorage exists: ${!!process.env.AzureWebJobsStorage}`);
    context.log(`SQL_CONN_STR exists: ${!!process.env.SQL_CONN_STR}`);

    // Log the raw message
    try {
      context.log(`Raw message type: ${typeof rawMessage}`);
      context.log(`Raw message: ${JSON.stringify(rawMessage)}`);
    } catch (e: any) {
      context.log(`Error stringifying raw message: ${e.message}`);
      context.log(`Raw message toString: ${String(rawMessage)}`);
    }
  } catch (outerError: any) {
    // Catch any errors in the initial logging
    context.error("Critical error in initial function setup", {
      error: outerError.message,
      stack: outerError.stack
    });
    throw outerError; // Rethrow to ensure the function fails
  }

  try {
    // 1) Parse the message
    let messageData: { blobName: string; timestamp: string };

    // Check if rawMessage is already an object with the expected properties
    if (typeof rawMessage === 'object' && rawMessage !== null &&
        'blobName' in rawMessage && 'timestamp' in rawMessage) {
      // It's already the right format, use it directly
      messageData = rawMessage as { blobName: string; timestamp: string };
      context.log("Message is already in the correct format");
    } else {
      // Try to parse it from a string
      const msg = String(rawMessage);
      context.log(`Raw message as string: ${msg}`);

      try {
        // Try direct JSON parse first
        messageData = JSON.parse(msg);
        context.log("Message parsed as JSON string");
      } catch (parseError: any) {
        context.error("Failed to parse message as JSON", {
          error: parseError.message,
          rawMessage: msg
        });

        // As a last resort, try base64 decoding
        try {
          const decodedMsg = Buffer.from(msg, "base64").toString("utf8");
          context.log(`Decoded message: ${decodedMsg}`);
          messageData = JSON.parse(decodedMsg);
          context.log("Message parsed after base64 decoding");
        } catch (decodeError: any) {
          context.error("Failed to decode and parse message", {
            error: decodeError.message,
            rawMessage: msg
          });
          throw new Error(`Invalid message format: ${decodeError.message}`);
        }
      }
    }

    const { blobName } = messageData;
    context.log(`Processing ZIP file: ${blobName}`);

    // 2) Download the ZIP file from blob storage
    context.log("Initializing blob service client");
    const blobSvc = BlobServiceClient.fromConnectionString(process.env.AzureWebJobsStorage!);
    const container = blobSvc.getContainerClient("zip-submissions");
    const blockBlobClient = container.getBlockBlobClient(blobName);

    // Check if blob exists
    context.log("Checking if blob exists");
    const exists = await blockBlobClient.exists();
    if (!exists) {
      context.error(`Blob ${blobName} not found`);
      throw new Error(`Blob ${blobName} not found`);
    }

    // Download the blob
    context.log("Downloading blob");
    const downloadResponse = await blockBlobClient.download(0);
    const chunks: Buffer[] = [];

    // Read the stream
    context.log("Reading blob stream");
    for await (const chunk of downloadResponse.readableStreamBody!) {
      chunks.push(Buffer.from(chunk));
    }

    // Combine chunks into a single buffer
    const zipBuffer = Buffer.concat(chunks);
    context.log(`Downloaded ZIP file (${zipBuffer.length} bytes)`);

    // 3) Extract the ZIP file
    context.log("Extracting ZIP file");
    let zip: JSZip;
    try {
      zip = await JSZip.loadAsync(zipBuffer);
      context.log("ZIP file loaded successfully");
    } catch (e: any) {
      context.error("ZIP parse error", {
        error: e.message,
        stack: e.stack
      });
      throw new Error(`Invalid ZIP file: ${e.message}`);
    }

    // 4) Find and process the JSON file
    context.log("Looking for JSON file in ZIP");
    const jsonEntry = Object.values(zip.files).find(f => f.name.toLowerCase().endsWith(".json"));
    if (!jsonEntry) {
      context.error("No JSON file found in ZIP");
      throw new Error("No JSON file found in ZIP");
    }

    context.log(`Found JSON file: ${jsonEntry.name}`);

    let payload: any;
    try {
      const jsonText = await jsonEntry.async("text");
      context.log(`JSON content length: ${jsonText.length} characters`);
      payload = JSON.parse(jsonText);
      context.log("JSON parsed successfully");
    } catch (e: any) {
      context.error("Bad JSON inside ZIP", {
        error: e.message,
        stack: e.stack
      });
      throw new Error(`Malformed JSON in ZIP: ${e.message}`);
    }

    // 5) Insert JSON data into SQL
    context.log("Preparing to insert data into SQL");
    let submissionId: number;
    try {
      // Get SQL connection
      const pool = await getSqlConnection(context);

      // Log SQL query parameters
      context.log("SQL parameters prepared", {
        payloadLength: JSON.stringify(payload).length,
        blobName: blobName
      });

      // Execute the query
      context.log("Executing SQL insert query");
      const result = await pool.request()
        .input('payload', sql.NVarChar(sql.MAX), JSON.stringify(payload))
        .input('blobName', sql.NVarChar(255), blobName)
        .query(`
          INSERT INTO dbo.FastFieldSubmissions (Payload, ZipBlobName)
          OUTPUT INSERTED.Id
          VALUES (@payload, @blobName)
        `);

      submissionId = result.recordset[0].Id;
      context.log(`Inserted submission with ID: ${submissionId}`);
    } catch (e: any) {
      context.error("SQL error", {
        error: e.message,
        stack: e.stack,
        sqlState: e.state,
        sqlCode: e.code,
        sqlNumber: e.number
      });
      throw new Error(`Database insert failed: ${e.message}`);
    }

    // 6) Process images directly and queue image processing tasks
    context.log("Preparing to process images");
    try {
      // Initialize blob storage client
      const blobSvc = BlobServiceClient.fromConnectionString(process.env.AzureWebJobsStorage!);
      const submissionsContainer = blobSvc.getContainerClient("submissions");
      await submissionsContainer.createIfNotExists();

      // Find all non-JSON files (images)
      context.log("Finding image files in ZIP");
      const imageEntries = Object.values(zip.files).filter(entry =>
        !entry.dir && entry !== jsonEntry
      );

      context.log(`Found ${imageEntries.length} images to process`);

      // Process images directly
      const urlMap: Record<string, string> = {};
      let processedImages = 0;

      for (const entry of imageEntries) {
        try {
          context.log(`Processing image: ${entry.name}`);

          // Extract the image data
          const imageData = await entry.async("nodebuffer");
          context.log(`Extracted image: ${entry.name} (${imageData.length} bytes)`);

          // Determine content type based on file extension
          let contentType = 'application/octet-stream';
          if (entry.name.toLowerCase().endsWith('.jpg') || entry.name.toLowerCase().endsWith('.jpeg')) {
            contentType = 'image/jpeg';
          } else if (entry.name.toLowerCase().endsWith('.png')) {
            contentType = 'image/png';
          } else if (entry.name.toLowerCase().endsWith('.gif')) {
            contentType = 'image/gif';
          } else if (entry.name.toLowerCase().endsWith('.pdf')) {
            contentType = 'application/pdf';
          }

          // Upload the image
          const imageBlobClient = submissionsContainer.getBlockBlobClient(`${submissionId}/${entry.name}`);
          await imageBlobClient.uploadData(imageData, {
            blobHTTPHeaders: { blobContentType: contentType },
            metadata: { submissionId: String(submissionId) }
          });

          const imageUrl = imageBlobClient.url;
          context.log(`Uploaded image to: ${imageUrl}`);

          // Add to URL map
          urlMap[entry.name] = imageUrl;
          processedImages++;
        } catch (imgError: any) {
          context.warn(`Failed to process image ${entry.name}: ${imgError.message}`);
        }
      }

      // Update the database with all image URLs
      try {
        // Get SQL connection
        const pool = await getSqlConnection(context);

        const result = await pool.request()
          .input('urls', sql.NVarChar(sql.MAX), JSON.stringify(urlMap))
          .input('id', sql.Int, submissionId)
          .query(`
            UPDATE dbo.FastFieldSubmissions
            SET AttachmentUrls = @urls
            WHERE Id = @id
          `);

        context.log(`Updated database with URLs for ${processedImages} images`);
      } catch (sqlError: any) {
        context.error("SQL update error", sqlError);
        throw new Error("Database update failed");
      }

      // Also queue images for background processing (for backward compatibility)
      const imageQueue = QueueServiceClient
        .fromConnectionString(process.env.AzureWebJobsStorage!)
        .getQueueClient("image-processing");

      context.log("Creating image-processing queue if it doesn't exist");
      await imageQueue.createIfNotExists();

      // Queue a message for each image
      let queuedImages = 0;
      for (const entry of imageEntries) {
        context.log(`Queueing image: ${entry.name}`);
        const message = {
          submissionId,
          imageName: entry.name,
          zipBlobName: blobName
        };

        await imageQueue.sendMessage(Buffer.from(JSON.stringify(message)).toString("base64"));
        queuedImages++;
      }

      context.log(`Successfully queued ${queuedImages} images for processing`);
    } catch (e: any) {
      context.error("Error processing images", {
        error: e.message,
        stack: e.stack
      });
      // Continue processing even if image processing fails
      context.log("Continuing despite image processing error");
    }

    // 7) Queue summary processing (if needed)
    context.log("Preparing to queue summary processing task");
    try {
      // Queue for DOCX document generation
      const summaryDocxQueue = QueueServiceClient
        .fromConnectionString(process.env.AzureWebJobsStorage!)
        .getQueueClient("summary-tasks-docx");

      context.log("Creating summary-tasks-docx queue if it doesn't exist");
      await summaryDocxQueue.createIfNotExists();

      context.log(`Queueing DOCX summary task for submission ${submissionId}`);
      await summaryDocxQueue.sendMessage(Buffer.from(String(submissionId)).toString("base64"));

      // Queue for XLSX spreadsheet generation
      const summaryXlsxQueue = QueueServiceClient
        .fromConnectionString(process.env.AzureWebJobsStorage!)
        .getQueueClient("summary-tasks-xlsx");

      context.log("Creating summary-tasks-xlsx queue if it doesn't exist");
      await summaryXlsxQueue.createIfNotExists();

      context.log(`Queueing XLSX summary task for submission ${submissionId}`);
      await summaryXlsxQueue.sendMessage(Buffer.from(JSON.stringify({ submissionId })).toString("base64"));

      context.log("Summary tasks queued successfully");
    } catch (e: any) {
      context.error("Error queueing summary tasks", {
        error: e.message,
        stack: e.stack
      });
      // Continue processing even if summary queueing fails
    }

    context.log(`ZIP processing complete for submission ${submissionId}`);
    return;
  } catch (error: any) {
    context.error("Function failed", {
      error: error.message,
      stack: error.stack,
      functionName: "processZipFile"
    });
    throw error; // Rethrow to trigger retry
  }
};

app.storageQueue("processZipFile", {
  queueName: "zip-processing",
  connection: "AzureWebJobsStorage",
  handler: processZipFile,
});
