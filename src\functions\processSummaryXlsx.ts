import { app, InvocationContext, Storage<PERSON>ueue<PERSON>andler } from "@azure/functions";
import * as sql from "mssql";
import * as XLSX from "xlsx";
import { BlobServiceClient } from "@azure/storage-blob";
import { ClientSecretCredential } from "@azure/identity";
import { Client } from "@microsoft/microsoft-graph-client";
import * as fs from "fs";
import * as path from "path";

// Load solution links data
const solutionLinksPath = path.join(__dirname, '../../solution-links.json');
const solutionLinksData = JSON.parse(fs.readFileSync(solutionLinksPath, 'utf8'));

// Define interfaces for better type safety
interface FormPayload {
  formId?: number;
  main_form_facility_name?: string;
  main_form_date?: string;
  section_2?: MachineSection[];
}

interface MachineSection {
  fields: {
    main_form_machine_title?: string;
    main_form_general_machine_subform?: Array<{
      general_machine_type?: string[];
      total_hrn?: number;
      field_S?: string[];
      field_F?: string[];
      field_P?: string[];
      assessment_level?: string[];
      solutions?: string[];
    }>;
  };
}

interface SubmissionData {
  id: number;
  payload: FormPayload;
}

// Helper function to create SQL connection (same as DOCX function)
async function createSqlConnection(context: InvocationContext): Promise<sql.ConnectionPool> {
  const sqlConnStr = process.env.SQL_CONN_STR;

  context.log(`SQL Environment check:`);
  context.log(`- SQL_CONN_STR: ${sqlConnStr ? 'SET' : 'NOT SET'}`);

  if (!sqlConnStr) {
    throw new Error('SQL_CONN_STR environment variable is not set');
  }

  context.log("Connecting to SQL database using connection string");
  const pool = await sql.connect(sqlConnStr);
  context.log("SQL connection established successfully");
  return pool;
}

// Helper function to execute with retry logic
async function executeWithRetry<T>(
  operation: () => Promise<T>,
  context: InvocationContext,
  maxRetries: number = 3
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      context.warn(`Attempt ${attempt} failed: ${error.message}`);
      if (attempt === maxRetries) {
        throw error;
      }
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
  throw new Error("Max retries exceeded");
}

// Helper function to format machine type
function formatMachineType(machineType: string[] | undefined): string {
  if (!machineType || machineType.length === 0) {
    return "Not specified";
  }

  const machineTypeMap: Record<string, string> = {
    "general": "General Machine",
    "complex": "Complex Machine System",
    "abb_wheel": "Abrasive Wheel Grinder / Bench or Pedestal Stand",
    "band_saw": "Bandsaw",
    "cnc": "CNC Machine",
    "drill_press": "Drill Press",
    "honing": "Honing Machine",
    "boring": "Horizontal Boring Machine",
    "horizontal_lathe": "Horizontal Lathe",
    "hyd_press": "Hydraulic Press",
    "ironworker": "Ironworker",
    "milling_machine": "Manual Mill",
    "mech_press": "Mechanical Press",
    "pipe_bender": "Pipe Bender",
    "pipe_thread": "Pipe Threading Machine",
    "press_brake": "Press Break",
    "shear": "Shear",
    "surf_grinder": "Surface Grinder",
    "vtl": "Vertical Turret Lathe",
    "other": "Other"
  };

  return machineTypeMap[machineType[0]] || machineType[0];
}

// Helper function to calculate PLr level
function calculatePlrLevel(S: string | undefined, F: string | undefined, P: string | undefined): string {
  if (!S || !F || !P) return "Not calculated";
  
  const totalScore = Number(S) + Number(F) + Number(P);
  
  if (totalScore === 7) return "A";
  if (totalScore === 8) return "B";
  if (totalScore === 9) return "C";
  if (totalScore === 10) return "D";
  if (totalScore === 11) return "E";
  
  return "Not calculated";
}

// Helper function to get HRN risk level
function getHrnRiskLevel(hrnScore: number): string {
  if (hrnScore < 2) return "Low Risk";
  if (hrnScore < 50) return "Medium Risk";
  return "High Risk";
}

// Helper function to format solutions for Excel with hyperlinks
function formatSolutions(machineSubform: any, context?: InvocationContext): any {
  const solutionFields = Object.keys(machineSubform).filter(key => key.startsWith('listpicker_'));
  const otherSolutionsText = machineSubform.multiline_4;

  if (context) {
    context.log(`Found ${solutionFields.length} solution fields: ${solutionFields.join(', ')}`);
    context.log(`Other solutions text: "${otherSolutionsText}"`);
  }

  const allSolutions: any[] = [];

  // Process each solution field
  for (const fieldKey of solutionFields) {
    const solutions = machineSubform[fieldKey];
    if (solutions && Array.isArray(solutions) && solutions.length > 0) {
      for (const solutionKey of solutions) {
        // Handle "other" option with custom text
        if (solutionKey === 'other' && otherSolutionsText) {
          allSolutions.push({ text: otherSolutionsText, url: null });
        } else if (solutionKey !== 'other') {
          // Get solution data from the JSON
          const solutionData = solutionLinksData[solutionKey];
          if (context) {
            context.log(`Processing solution key: "${solutionKey}"`);
            context.log(`Solution data found: ${solutionData ? 'YES' : 'NO'}`);
          }

          if (solutionData) {
            if (context) {
              context.log(`Solution name: "${solutionData.name}"`);
              context.log(`Solution has ${solutionData.links ? solutionData.links.length : 0} links`);
            }

            // If solution has links
            if (solutionData.links && solutionData.links.length > 0) {
              if (solutionData.links.length === 1) {
                // Single link
                const solutionObj = {
                  text: solutionData.name,
                  url: solutionData.links[0].url
                };
                allSolutions.push(solutionObj);
                if (context) {
                  context.log(`Added single link solution: "${solutionObj.text}" -> "${solutionObj.url}"`);
                }
              } else {
                // Multiple links - add each as separate entry
                solutionData.links.forEach((link: any) => {
                  const linkText = link.name ? `${solutionData.name} (${link.name})` : solutionData.name;
                  const solutionObj = {
                    text: linkText,
                    url: link.url
                  };
                  allSolutions.push(solutionObj);
                  if (context) {
                    context.log(`Added multi-link solution: "${solutionObj.text}" -> "${solutionObj.url}"`);
                  }
                });
              }
            } else {
              // Just the solution name (no links)
              const solutionObj = { text: solutionData.name, url: null };
              allSolutions.push(solutionObj);
              if (context) {
                context.log(`Added no-link solution: "${solutionObj.text}"`);
              }
            }
          } else {
            // Fallback to the key if not found in JSON
            const solutionObj = { text: solutionKey, url: null };
            allSolutions.push(solutionObj);
            if (context) {
              context.log(`Added fallback solution: "${solutionObj.text}"`);
            }
          }
        }
      }
    }
  }

  // If there's custom text but no "other" option was selected, still include it
  if (otherSolutionsText && !solutionFields.some(fieldKey => {
    const solutions = machineSubform[fieldKey];
    return solutions && Array.isArray(solutions) && solutions.includes('other');
  })) {
    allSolutions.push({ text: otherSolutionsText, url: null });
  }

  return allSolutions.length > 0 ? allSolutions : [{ text: "No solutions specified", url: null }];
}

/**
 * Creates an Excel spreadsheet with machine assessment data
 */
async function createExcelSpreadsheet(
  submission: SubmissionData,
  context: InvocationContext
): Promise<Buffer> {
  const { payload } = submission;

  context.log(`Creating Excel spreadsheet for submission ${submission.id}`);

  // Get machines data and sort by Total HRN Score (highest first)
  const machines = (payload.section_2 || []).sort((a, b) => {
    const getMachineHRN = (machine: MachineSection): number => {
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0];
      if (!machineSubform || machineSubform.total_hrn === undefined) {
        return -1; // Put machines without HRN scores at the end
      }
      return Number(machineSubform.total_hrn) || -1;
    };

    const hrnA = getMachineHRN(a);
    const hrnB = getMachineHRN(b);

    // Sort in descending order (highest HRN first)
    return hrnB - hrnA;
  });

  // Create worksheet data
  const worksheetData = [];
  const machinesWithSolutions: any[] = []; // Track solutions for hyperlink processing

  // Add header row
  worksheetData.push([
    "Machine Name",
    "Machine Type",
    "HRN Score",
    "HRN Risk Level",
    "PLr Level",
    "Solutions"
  ]);

  // Add data rows
  for (let i = 0; i < machines.length; i++) {
    const machine = machines[i];
    const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};
    
    const machineName = machine.fields.main_form_machine_title || `Machine ${i + 1}`;
    const machineType = formatMachineType(machineSubform.general_machine_type);
    const hrnScore = machineSubform.total_hrn || 0;
    const hrnRiskLevel = getHrnRiskLevel(hrnScore);
    
    // Calculate PLr level
    const plrLevel = calculatePlrLevel(
      machineSubform.field_S?.[0],
      machineSubform.field_F?.[0], 
      machineSubform.field_P?.[0]
    );
    
    const solutions = formatSolutions(machineSubform, context);

    // Create solutions cell with hyperlink formula (simplified approach)
    const solutionsText = solutions.map((sol: any) => sol.text).join("\n"); // Use line breaks instead of semicolons
    const primarySolution = solutions.find((sol: any) => sol.url);

    // Create cell with HYPERLINK formula if available (no complex styling)
    let solutionsCell: any;
    if (primarySolution) {
      // Use Excel HYPERLINK formula - Excel will automatically style it as a link
      solutionsCell = {
        f: `HYPERLINK("${primarySolution.url}","${solutionsText}")`, // Formula
        t: 's' // String type
      };
      context.log(`Created HYPERLINK formula for: ${solutionsText.replace(/\n/g, '; ')} -> ${primarySolution.url}`);
    } else {
      solutionsCell = solutionsText;
    }

    worksheetData.push([
      machineName,
      machineType,
      hrnScore,
      hrnRiskLevel,
      plrLevel,
      solutionsCell
    ]);

    // Store solutions data separately for hyperlink processing (backup method)
    machinesWithSolutions.push({
      rowIndex: i + 1, // +1 because we have a header row
      solutions: solutions
    });
  }

  // Create workbook and worksheet
  context.log(`=== CREATING WORKSHEET ===`);
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
  context.log(`Worksheet created successfully`);

  // Set column widths
  worksheet['!cols'] = [
    { width: 25 }, // Machine Name
    { width: 30 }, // Machine Type
    { width: 12 }, // HRN Score
    { width: 15 }, // HRN Risk Level
    { width: 12 }, // PLr Level
    { width: 60 }  // Solutions (wider for multi-line content)
  ];
  context.log(`Column widths set`);

  // Enable text wrapping for solutions column (column F)
  for (let i = 1; i < worksheetData.length; i++) { // Start from 1 to skip header
    const cellAddress = XLSX.utils.encode_cell({ r: i, c: 5 }); // Column F (Solutions)
    if (worksheet[cellAddress]) {
      if (!worksheet[cellAddress].s) {
        worksheet[cellAddress].s = {};
      }
      if (!worksheet[cellAddress].s.alignment) {
        worksheet[cellAddress].s.alignment = {};
      }
      worksheet[cellAddress].s.alignment.wrapText = true;
      worksheet[cellAddress].s.alignment.vertical = 'top'; // Align to top for better readability
    }
  }
  context.log(`Text wrapping enabled for solutions column`);

  // Add hyperlinks to solutions column using correct SheetJS approach
  context.log(`=== STARTING HYPERLINK PROCESSING ===`);
  context.log(`machinesWithSolutions array length: ${machinesWithSolutions.length}`);
  context.log(`worksheetData length: ${worksheetData.length}`);
  context.log(`Processing hyperlinks for ${machinesWithSolutions.length} machines`);

  for (const machineData of machinesWithSolutions) {
    const rowIndex = machineData.rowIndex;
    const solutions = machineData.solutions;

    context.log(`Machine row ${rowIndex}: Found ${solutions.length} solutions`);

    // Find the first solution with a URL for the hyperlink
    const primarySolution = solutions.find((sol: any) => sol.url);

    if (primarySolution) {
      context.log(`Found primary solution with URL: ${primarySolution.text} -> ${primarySolution.url}`);

      // Get the cell address for the solutions column (column F, index 5)
      const cellAddress = XLSX.utils.encode_cell({ r: rowIndex, c: 5 });
      context.log(`Setting hyperlink on cell: ${cellAddress}`);

      // Make sure the cell exists
      if (worksheet[cellAddress]) {
        // Add hyperlink to the existing cell
        worksheet[cellAddress].l = {
          Target: primarySolution.url,
          Tooltip: `Click to view: ${primarySolution.text}`
        };
        context.log(`Successfully set hyperlink on ${cellAddress}`);
      } else {
        context.warn(`Cell ${cellAddress} does not exist in worksheet`);
      }
    } else {
      context.log(`No solutions with URLs found for machine row ${rowIndex}`);
      // Log what solutions we do have
      solutions.forEach((sol: any, index: number) => {
        context.log(`  Solution ${index}: text="${sol.text}", url="${sol.url}"`);
      });
    }
  }

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Machine Assessment");

  // Convert to buffer
  const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  
  context.log(`Excel spreadsheet created with ${machines.length} machines`);
  
  return excelBuffer;
}

/**
 * Helper function to format date for folder names
 */
function formatDateForFolder(dateString: string | undefined): string {
  if (!dateString) {
    return new Date().toISOString().split('T')[0]; // Default to today's date
  }

  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  } catch {
    return new Date().toISOString().split('T')[0]; // Fallback to today's date
  }
}

/**
 * Helper function to format date for filenames (M-D-YYYY format)
 */
function formatDateForFilename(dateString: string | undefined): string {
  if (!dateString) {
    const today = new Date();
    return `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
  }

  try {
    const date = new Date(dateString);
    return `${date.getMonth() + 1}-${date.getDate()}-${date.getFullYear()}`;
  } catch {
    const today = new Date();
    return `${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}`;
  }
}

/**
 * Helper function to sanitize folder names
 */
function sanitizeFolderName(name: string): string {
  // Remove or replace characters that are not allowed in SharePoint folder names
  return name
    .replace(/[<>:"/\\|?*]/g, '_') // Replace invalid characters with underscore
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim()
    .substring(0, 100); // Limit length to 100 characters
}

/**
 * Uploads Excel file to SharePoint with proper folder structure
 */
async function uploadToSharePoint(
  submission: SubmissionData,
  excelBuffer: Buffer,
  context: InvocationContext
): Promise<void> {
  try {
    // Create Microsoft Graph client
    const credential = new ClientSecretCredential(
      process.env.AZURE_TENANT_ID!,
      process.env.AZURE_CLIENT_ID!,
      process.env.AZURE_CLIENT_SECRET!
    );

    const graphClient = Client.initWithMiddleware({
      authProvider: {
        getAccessToken: async () => {
          const tokenResponse = await credential.getToken("https://graph.microsoft.com/.default");
          return tokenResponse.token;
        }
      }
    });

    const siteId = process.env.SHAREPOINT_SITE_ID!;
    let driveId = process.env.SHAREPOINT_DRIVE_ID;

    // If driveId is not set, get the default drive ID
    if (!driveId) {
      context.log("SHAREPOINT_DRIVE_ID not set, getting default drive ID");
      try {
        const driveResponse = await graphClient
          .api(`/sites/${siteId}/drive`)
          .get();
        driveId = driveResponse.id;
        context.log(`Retrieved default drive ID: ${driveId}`);
      } catch (error: any) {
        context.error(`Failed to get default drive ID: ${error.message}`);
        throw new Error(`Cannot access SharePoint drive: ${error.message}`);
      }
    } else {
      context.log(`Using configured drive ID: ${driveId}`);
    }

    // Create folder structure: Facility Name / Assessment Date
    const facilityName = sanitizeFolderName(submission.payload.main_form_facility_name || "Unknown Facility");
    const assessmentDate = formatDateForFolder(submission.payload.main_form_date);

    context.log(`Creating folder structure: Summaries/${facilityName}/${assessmentDate}`);
    context.log(`Raw facility name from payload: "${submission.payload.main_form_facility_name}"`);
    context.log(`Sanitized facility name: "${facilityName}"`);
    context.log(`Assessment date: "${assessmentDate}"`);

    // First, ensure the Summaries folder exists and get its ID
    let summariesFolderId: string;
    try {
      context.log("Getting Summaries folder ID");
      const summariesFolderResponse = await graphClient
        .api(`/sites/${siteId}/drives/${driveId}/root:/Summaries`)
        .get();
      summariesFolderId = summariesFolderResponse.id;
      context.log(`Found Summaries folder (ID: ${summariesFolderId})`);
    } catch (error: any) {
      context.error(`Summaries folder not found: ${error.message}`);
      throw new Error(`Summaries folder not accessible: ${error.message}`);
    }

    // Create or get facility folder inside Summaries
    let facilityFolderId: string;
    try {
      // Try to get existing facility folder by listing all children and finding the match
      context.log(`Checking for existing facility folder: "${facilityName}" in Summaries`);
      const facilityFolderResponse = await graphClient
        .api(`/sites/${siteId}/drives/${driveId}/items/${summariesFolderId}/children`)
        .get();

      // Look for existing folder with matching name
      const existingFolder = facilityFolderResponse.value?.find((item: any) =>
        item.folder && item.name === facilityName
      );

      if (existingFolder) {
        facilityFolderId = existingFolder.id;
        context.log(`Found existing facility folder: "${facilityName}" (ID: ${facilityFolderId})`);
      } else {
        context.log(`No existing facility folder found with name: "${facilityName}"`);
        throw new Error("Facility folder not found");
      }
    } catch (error: any) {
      context.log(`Facility folder not found, creating new one. Error: ${error.message}`);
      try {
        // Create facility folder if it doesn't exist
        context.log(`Creating new facility folder: "${facilityName}" in Summaries folder ${summariesFolderId}`);
        const facilityFolderResponse = await graphClient
          .api(`/sites/${siteId}/drives/${driveId}/items/${summariesFolderId}/children`)
          .post({
            name: facilityName,
            folder: {},
            "@microsoft.graph.conflictBehavior": "rename"
          });
        facilityFolderId = facilityFolderResponse.id;
        context.log(`Successfully created new facility folder: "${facilityName}" (ID: ${facilityFolderId})`);
      } catch (createError: any) {
        context.error(`Failed to create facility folder: ${createError.message}`);
        throw new Error(`Folder creation failed: ${createError.message}`);
      }
    }

    // Create or get assessment date folder
    let dateFolderId: string;
    try {
      // Try to get existing date folder
      context.log(`Checking for existing date folder: ${assessmentDate} in facility folder ${facilityFolderId}`);
      const dateFolderResponse = await graphClient
        .api(`/sites/${siteId}/drives/${driveId}/items/${facilityFolderId}/children`)
        .get();

      // Look for existing folder with matching name
      const existingDateFolder = dateFolderResponse.value?.find((item: any) =>
        item.folder && item.name === assessmentDate
      );

      if (existingDateFolder) {
        dateFolderId = existingDateFolder.id;
        context.log(`Found existing date folder: ${assessmentDate} (ID: ${dateFolderId})`);
      } else {
        throw new Error("Date folder not found in response");
      }
    } catch (error: any) {
      context.log(`Date folder not found, creating new one. Error: ${error.message}`);
      try {
        // Create date folder if it doesn't exist
        const dateFolderResponse = await graphClient
          .api(`/sites/${siteId}/drives/${driveId}/items/${facilityFolderId}/children`)
          .post({
            name: assessmentDate,
            folder: {},
            "@microsoft.graph.conflictBehavior": "rename"
          });
        dateFolderId = dateFolderResponse.id;
        context.log(`Created new date folder: ${assessmentDate} (ID: ${dateFolderId})`);
      } catch (createError: any) {
        context.error(`Failed to create date folder: ${createError.message}`);
        // Fallback: use facility folder directly
        context.log("Falling back to facility folder for file upload");
        dateFolderId = facilityFolderId;
      }
    }

    // Generate filename with clean format
    const formattedDate = formatDateForFilename(submission.payload.main_form_date);
    const filename = `Machine Assessment ${submission.id} ${formattedDate}.xlsx`;

    context.log(`Uploading Excel file: ${filename} to folder ID: ${dateFolderId}`);

    // Upload Excel file to the date folder
    const uploadResponse = await graphClient
      .api(`/sites/${siteId}/drives/${driveId}/items/${dateFolderId}:/${filename}:/content`)
      .put(excelBuffer);

    context.log(`Excel file uploaded successfully: ${uploadResponse.webUrl}`);

  } catch (error: any) {
    context.error("Failed to upload Excel file to SharePoint", {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Main function to process XLSX generation
 */
const processSummaryXlsx: StorageQueueHandler = async (
  rawMessage: unknown,
  context: InvocationContext
): Promise<void> => {
  try {
    context.log("=== XLSX FUNCTION STARTED ===");
    context.log(`Raw message received: ${JSON.stringify(rawMessage)}`);

    // Parse the submission ID from the queue message (same format as DOCX)
    const submissionId = parseInt(rawMessage as string);
    context.log(`Processing XLSX generation for submission ID: ${submissionId}`);

    // Get submission data from database (same pattern as DOCX function)
    let pool: sql.ConnectionPool;
    try {
      pool = await createSqlConnection(context);
    } catch (error: any) {
      context.error("Failed to connect to SQL database", error);
      throw new Error(`SQL connection failed: ${error.message}`);
    }

    let submission: SubmissionData;
    try {
      const result = await pool.request()
        .input('id', sql.Int, submissionId)
        .query`SELECT Id, Payload FROM dbo.FastFieldSubmissions WHERE Id = @id`;

      if (result.recordset.length === 0) {
        throw new Error(`Submission with ID ${submissionId} not found`);
      }

      const record = result.recordset[0];
      submission = {
        id: record.Id,
        payload: JSON.parse(record.Payload)
      } as SubmissionData;

      context.log(`Loaded submission data for ID ${submissionId}`);
    } finally {
      try {
        await pool.close();
      } catch (closeError) {
        context.warn("Error closing SQL connection:", closeError);
      }
    }

    // Create Excel spreadsheet
    const excelBuffer = await createExcelSpreadsheet(submission, context);

    // Upload to SharePoint with folder structure
    await uploadToSharePoint(submission, excelBuffer, context);

    context.log("=== XLSX FUNCTION COMPLETED SUCCESSFULLY ===");

  } catch (error: any) {
    context.error("=== XLSX FUNCTION FAILED ===", {
      error: error.message,
      stack: error.stack,
      functionName: 'processSummaryXlsx'
    });
    throw error;
  }
};

app.storageQueue("processSummaryXlsx", {
  queueName: "summary-tasks-xlsx",
  connection: "AzureWebJobsStorage",
  handler: processSummaryXlsx,
});
