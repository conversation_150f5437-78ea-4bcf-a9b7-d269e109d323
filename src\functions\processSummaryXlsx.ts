import { app, InvocationContext, Storage<PERSON>ueue<PERSON>andler } from "@azure/functions";
import * as sql from "mssql";
import * as XLSX from "xlsx";
import { BlobServiceClient } from "@azure/storage-blob";
import { ClientSecretCredential } from "@azure/identity";
import { Client } from "@microsoft/microsoft-graph-client";
import * as fs from "fs";
import * as path from "path";

// Load solution links data
const solutionLinksPath = path.join(__dirname, '../../solution-links.json');
const solutionLinksData = JSON.parse(fs.readFileSync(solutionLinksPath, 'utf8'));

// Define interfaces for better type safety
interface FormPayload {
  formId?: number;
  main_form_facility_name?: string;
  main_form_date?: string;
  section_2?: MachineSection[];
}

interface MachineSection {
  fields: {
    main_form_machine_title?: string;
    main_form_general_machine_subform?: Array<{
      general_machine_type?: string[];
      total_hrn?: number;
      field_S?: string[];
      field_F?: string[];
      field_P?: string[];
      assessment_level?: string[];
      solutions?: string[];
    }>;
  };
}

interface SubmissionData {
  id: number;
  payload: FormPayload;
}

// Helper function to create SQL connection with retry logic
async function createSqlConnection(context: InvocationContext): Promise<sql.ConnectionPool> {
  const config: sql.config = {
    user: process.env.SQL_USER!,
    password: process.env.SQL_PASSWORD!,
    database: process.env.SQL_DATABASE!,
    server: process.env.SQL_SERVER!,
    pool: {
      max: 10,
      min: 0,
      idleTimeoutMillis: 30000
    },
    options: {
      encrypt: true,
      trustServerCertificate: false
    }
  };

  const pool = new sql.ConnectionPool(config);
  await pool.connect();
  context.log("SQL connection established successfully");
  return pool;
}

// Helper function to execute with retry logic
async function executeWithRetry<T>(
  operation: () => Promise<T>,
  context: InvocationContext,
  maxRetries: number = 3
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      context.warn(`Attempt ${attempt} failed: ${error.message}`);
      if (attempt === maxRetries) {
        throw error;
      }
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
  throw new Error("Max retries exceeded");
}

// Helper function to format machine type
function formatMachineType(machineType: string[] | undefined): string {
  if (!machineType || machineType.length === 0) {
    return "Not specified";
  }

  const machineTypeMap: Record<string, string> = {
    "general": "General Machine",
    "complex": "Complex Machine System",
    "abb_wheel": "Abrasive Wheel Grinder / Bench or Pedestal Stand",
    "band_saw": "Bandsaw",
    "cnc": "CNC Machine",
    "drill_press": "Drill Press",
    "honing": "Honing Machine",
    "boring": "Horizontal Boring Machine",
    "horizontal_lathe": "Horizontal Lathe",
    "hyd_press": "Hydraulic Press",
    "ironworker": "Ironworker",
    "milling_machine": "Manual Mill",
    "mech_press": "Mechanical Press",
    "pipe_bender": "Pipe Bender",
    "pipe_thread": "Pipe Threading Machine",
    "press_brake": "Press Break",
    "shear": "Shear",
    "surf_grinder": "Surface Grinder",
    "vtl": "Vertical Turret Lathe",
    "other": "Other"
  };

  return machineTypeMap[machineType[0]] || machineType[0];
}

// Helper function to calculate PLr level
function calculatePlrLevel(S: string | undefined, F: string | undefined, P: string | undefined): string {
  if (!S || !F || !P) return "Not calculated";
  
  const totalScore = Number(S) + Number(F) + Number(P);
  
  if (totalScore === 7) return "A";
  if (totalScore === 8) return "B";
  if (totalScore === 9) return "C";
  if (totalScore === 10) return "D";
  if (totalScore === 11) return "E";
  
  return "Not calculated";
}

// Helper function to get HRN risk level
function getHrnRiskLevel(hrnScore: number): string {
  if (hrnScore < 2) return "Low Risk";
  if (hrnScore < 50) return "Medium Risk";
  return "High Risk";
}

// Helper function to format solutions
function formatSolutions(solutions: string[] | undefined): string {
  if (!solutions || solutions.length === 0) {
    return "No solutions specified";
  }

  return solutions.map(solution => {
    // Check if solution has a link in solution-links.json
    const solutionLink = solutionLinksData[solution];
    if (solutionLink) {
      return `${solution} (${solutionLink})`;
    }
    return solution;
  }).join("; ");
}

/**
 * Creates an Excel spreadsheet with machine assessment data
 */
async function createExcelSpreadsheet(
  submission: SubmissionData,
  context: InvocationContext
): Promise<Buffer> {
  const { payload } = submission;

  context.log(`Creating Excel spreadsheet for submission ${submission.id}`);

  // Get machines data and sort by Total HRN Score (highest first)
  const machines = (payload.section_2 || []).sort((a, b) => {
    const getMachineHRN = (machine: MachineSection): number => {
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0];
      if (!machineSubform || machineSubform.total_hrn === undefined) {
        return -1; // Put machines without HRN scores at the end
      }
      return Number(machineSubform.total_hrn) || -1;
    };

    const hrnA = getMachineHRN(a);
    const hrnB = getMachineHRN(b);

    // Sort in descending order (highest HRN first)
    return hrnB - hrnA;
  });

  // Create worksheet data
  const worksheetData = [];

  // Add header row
  worksheetData.push([
    "Machine Name",
    "Machine Type", 
    "HRN Score",
    "HRN Risk Level",
    "PLr Level",
    "Solutions"
  ]);

  // Add data rows
  for (let i = 0; i < machines.length; i++) {
    const machine = machines[i];
    const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};
    
    const machineName = machine.fields.main_form_machine_title || `Machine ${i + 1}`;
    const machineType = formatMachineType(machineSubform.general_machine_type);
    const hrnScore = machineSubform.total_hrn || 0;
    const hrnRiskLevel = getHrnRiskLevel(hrnScore);
    
    // Calculate PLr level
    const plrLevel = calculatePlrLevel(
      machineSubform.field_S?.[0],
      machineSubform.field_F?.[0], 
      machineSubform.field_P?.[0]
    );
    
    const solutions = formatSolutions(machineSubform.solutions);

    worksheetData.push([
      machineName,
      machineType,
      hrnScore,
      hrnRiskLevel,
      plrLevel,
      solutions
    ]);
  }

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  worksheet['!cols'] = [
    { width: 25 }, // Machine Name
    { width: 30 }, // Machine Type
    { width: 12 }, // HRN Score
    { width: 15 }, // HRN Risk Level
    { width: 12 }, // PLr Level
    { width: 50 }  // Solutions
  ];

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Machine Assessment");

  // Convert to buffer
  const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  
  context.log(`Excel spreadsheet created with ${machines.length} machines`);
  
  return excelBuffer;
}

/**
 * Helper function to format date for folder names
 */
function formatDateForFolder(dateString: string | undefined): string {
  if (!dateString) {
    return new Date().toISOString().split('T')[0]; // Default to today's date
  }

  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  } catch {
    return new Date().toISOString().split('T')[0]; // Fallback to today's date
  }
}

/**
 * Helper function to sanitize folder names
 */
function sanitizeFolderName(name: string): string {
  // Remove or replace characters that are not allowed in SharePoint folder names
  return name
    .replace(/[<>:"/\\|?*]/g, '_') // Replace invalid characters with underscore
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim()
    .substring(0, 100); // Limit length to 100 characters
}

/**
 * Uploads Excel file to SharePoint with proper folder structure
 */
async function uploadToSharePoint(
  submission: SubmissionData,
  excelBuffer: Buffer,
  context: InvocationContext
): Promise<void> {
  try {
    // Create Microsoft Graph client
    const credential = new ClientSecretCredential(
      process.env.AZURE_TENANT_ID!,
      process.env.AZURE_CLIENT_ID!,
      process.env.AZURE_CLIENT_SECRET!
    );

    const graphClient = Client.initWithMiddleware({
      authProvider: {
        getAccessToken: async () => {
          const tokenResponse = await credential.getToken("https://graph.microsoft.com/.default");
          return tokenResponse.token;
        }
      }
    });

    const siteId = process.env.SHAREPOINT_SITE_ID!;
    let driveId = process.env.SHAREPOINT_DRIVE_ID;

    // If driveId is not set, get the default drive ID
    if (!driveId) {
      context.log("SHAREPOINT_DRIVE_ID not set, getting default drive ID");
      try {
        const driveResponse = await graphClient
          .api(`/sites/${siteId}/drive`)
          .get();
        driveId = driveResponse.id;
        context.log(`Retrieved default drive ID: ${driveId}`);
      } catch (error: any) {
        context.error(`Failed to get default drive ID: ${error.message}`);
        throw new Error(`Cannot access SharePoint drive: ${error.message}`);
      }
    } else {
      context.log(`Using configured drive ID: ${driveId}`);
    }

    // Create folder structure: Facility Name / Assessment Date
    const facilityName = sanitizeFolderName(submission.payload.main_form_facility_name || "Unknown Facility");
    const assessmentDate = formatDateForFolder(submission.payload.main_form_date);

    context.log(`Creating folder structure: ${facilityName}/${assessmentDate}`);

    // Create or get facility folder
    let facilityFolderId: string;
    try {
      // Try to get existing facility folder
      const facilityFolderResponse = await graphClient
        .api(`/sites/${siteId}/drives/${driveId}/root:/Summaries/${facilityName}`)
        .get();
      facilityFolderId = facilityFolderResponse.id;
      context.log(`Found existing facility folder: ${facilityName}`);
    } catch (error) {
      // Create facility folder if it doesn't exist
      const facilityFolderResponse = await graphClient
        .api(`/sites/${siteId}/drives/${driveId}/root:/Summaries:/children`)
        .post({
          name: facilityName,
          folder: {},
          "@microsoft.graph.conflictBehavior": "rename"
        });
      facilityFolderId = facilityFolderResponse.id;
      context.log(`Created new facility folder: ${facilityName}`);
    }

    // Create or get assessment date folder
    let dateFolderId: string;
    try {
      // Try to get existing date folder
      const dateFolderResponse = await graphClient
        .api(`/sites/${siteId}/drives/${driveId}/items/${facilityFolderId}:/children`)
        .filter(`name eq '${assessmentDate}'`)
        .get();

      if (dateFolderResponse.value && dateFolderResponse.value.length > 0) {
        dateFolderId = dateFolderResponse.value[0].id;
        context.log(`Found existing date folder: ${assessmentDate}`);
      } else {
        throw new Error("Date folder not found");
      }
    } catch (error) {
      // Create date folder if it doesn't exist
      const dateFolderResponse = await graphClient
        .api(`/sites/${siteId}/drives/${driveId}/items/${facilityFolderId}/children`)
        .post({
          name: assessmentDate,
          folder: {},
          "@microsoft.graph.conflictBehavior": "rename"
        });
      dateFolderId = dateFolderResponse.id;
      context.log(`Created new date folder: ${assessmentDate}`);
    }

    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `Machine_Assessment_Summary_${submission.id}_${timestamp}.xlsx`;

    // Upload Excel file to the date folder
    const uploadResponse = await graphClient
      .api(`/sites/${siteId}/drives/${driveId}/items/${dateFolderId}:/${filename}:/content`)
      .putStream(excelBuffer);

    context.log(`Excel file uploaded successfully: ${uploadResponse.webUrl}`);

  } catch (error: any) {
    context.error("Failed to upload Excel file to SharePoint", {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Main function to process XLSX generation
 */
const processSummaryXlsx: StorageQueueHandler = async (
  queueItem: any,
  context: InvocationContext
): Promise<void> => {
  try {
    const submissionId = queueItem.submissionId;
    context.log(`Processing submission ID: ${submissionId}`);

    // Get submission data from database
    const submission = await executeWithRetry(async () => {
      let pool: sql.ConnectionPool | null = null;

      try {
        pool = await createSqlConnection(context);

        const result = await pool.request()
          .input('id', sql.Int, submissionId)
          .query`SELECT Id, Payload FROM dbo.FastFieldSubmissions WHERE Id = @id`;

        if (result.recordset.length === 0) {
          throw new Error(`Submission with ID ${submissionId} not found`);
        }

        const record = result.recordset[0];
        return {
          id: record.Id,
          payload: JSON.parse(record.Payload)
        } as SubmissionData;
      } finally {
        if (pool) {
          try {
            await pool.close();
          } catch (closeError) {
            context.warn("Error closing SQL connection:", closeError);
          }
        }
      }
    }, context);

    context.log(`Loaded submission data for ID ${submissionId}`);

    // Create Excel spreadsheet
    const excelBuffer = await createExcelSpreadsheet(submission, context);

    // Upload to SharePoint with folder structure
    await uploadToSharePoint(submission, excelBuffer, context);

    context.log("Excel spreadsheet created and uploaded successfully");

  } catch (error: any) {
    context.error("Function failed", {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

app.storageQueue("processSummaryXlsx", {
  queueName: "summary-tasks-xlsx",
  connection: "AzureWebJobsStorage",
  handler: processSummaryXlsx,
});
