import { app, Invocation<PERSON>ontext, St<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@azure/functions";
import * as sql from "mssql";
import htmlToDocx from "html-to-docx";
import { BlobServiceClient } from "@azure/storage-blob";
import { ClientSecretCredential } from "@azure/identity";
import { Client } from "@microsoft/microsoft-graph-client";
import axios from "axios";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

// Define interfaces for better type safety
interface FormPayload {
  formId?: number;
  formName?: string;
  submissionId?: string;
  accountId?: number;
  main_form_facility_name?: string;
  main_form_first_name?: string;
  main_form_last_name?: string;
  main_form_email_address?: string;
  main_form_phone_number?: string;
  main_form_date?: string;
  main_form_nameplate?: string;
  formMetaData?: any;
  section_2?: MachineSection[];
  [key: string]: any;
}

interface MachineSection {
  sectionCounter: number;
  appended?: string;
  fields: {
    main_form_machine_title?: string;
    main_form_machine_type?: string[];
    main_form_general_machine_subform?: MachineSubform[];
    [key: string]: any;
  };
}

interface MachineSubform {
  reserved_instanceGuid?: string;
  general_machine_type?: string[];
  assessment_level?: string[];
  machine_name_and_id?: string;
  location_of_machine?: string;
  department?: string;
  machine_manufacturer?: string;
  machine_model_number?: string;
  machine_serial_number?: string;
  operator_names?: string;
  machine_description?: string;
  is_machine_in_operation?: number;
  was_machine_observed_during_observation?: number;
  known_injuries_associated_with_machine?: number;
  photo_of_machine_nameplate?: PhotoWithComment[];
  photo_of_machine_id_num?: PhotoWithComment[];
  photo_of_machine_front?: PhotoWithComment[];
  photo_of_machine_right?: PhotoWithComment[];
  photo_of_machine_back?: PhotoWithComment[];
  photo_of_machine_left?: PhotoWithComment[];
  section_40?: HazardSection[];
  [key: string]: any;
}

interface PhotoWithComment {
  comment: string;
  photo: string;
}

interface HazardSection {
  sectionCounter: number;
  appended?: string;
  fields: {
    field_403?: string; // Hazard name
    field_404?: PhotoWithComment[]; // Hazard photo
    field_405?: string; // Hazard description
    field_406?: string[]; // Recommended solutions
    multiline_5?: string; // Additional notes
    [key: string]: any;
  };
}

interface SubmissionData {
  id: number;
  payload: FormPayload;
  attachmentUrls?: Record<string, string>;
  zipBlobName?: string;
  submissionDate?: Date;
}

export const processSummaryHtml: StorageQueueHandler = async (
  rawMessage: unknown,
  context: InvocationContext
): Promise<void> => {
  // Create a temporary directory for image downloads
  const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'odiz-images-'));

  try {
    // 1) Decode the submission ID
    const msg = String(rawMessage);
    const submissionId = /^\d+$/.test(msg)
      ? parseInt(msg, 10)
      : parseInt(Buffer.from(msg, "base64").toString("utf8"), 10);
    context.log("Processing submission ID:", submissionId);

    // 2) Load the submission data from SQL
    let pool: sql.ConnectionPool;
    try {
      pool = await sql.connect(process.env.SQL_CONN_STR!);
      context.log("Connected to SQL database");
    } catch (error: any) {
      context.error("Failed to connect to SQL database", error);
      throw new Error(`SQL connection failed: ${error.message}`);
    }

    let submission: SubmissionData;
    try {
      const { recordset } = await pool.request()
        .input('id', sql.Int, submissionId)
        .query(`
          SELECT Id, Payload, AttachmentUrls, ZipBlobName, GETDATE() as SubmissionDate
          FROM dbo.FastFieldSubmissions
          WHERE Id = @id
        `);

      if (!recordset || recordset.length === 0) {
        throw new Error(`Submission with ID ${submissionId} not found`);
      }

      submission = {
        id: recordset[0].Id,
        payload: JSON.parse(recordset[0].Payload),
        attachmentUrls: recordset[0].AttachmentUrls ? JSON.parse(recordset[0].AttachmentUrls) : {},
        zipBlobName: recordset[0].ZipBlobName,
        submissionDate: recordset[0].SubmissionDate
      };

      context.log(`Loaded submission data for ID ${submissionId}`);
    } catch (error: any) {
      context.error("Failed to load submission data", error);
      throw new Error(`Failed to load submission data: ${error.message}`);
    }

    // 3) Download all images referenced in the submission using Azure Storage SDK
    const imageMap: Record<string, string> = {};
    try {
      context.log("Downloading images from blob storage for embedding in document");

      // Log the full submission data for debugging
      context.log("Submission ID: " + submission.id);
      context.log("Attachment URLs count: " + Object.keys(submission.attachmentUrls || {}).length);
      context.log("Attachment URLs: " + JSON.stringify(submission.attachmentUrls || {}));
      context.log("Zip Blob Name: " + submission.zipBlobName);

      // Initialize the BlobServiceClient with connection string
      const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AzureWebJobsStorage!);

      // List all containers for debugging
      context.log("Listing all containers in storage account:");
      let containerCount = 0;
      for await (const container of blobServiceClient.listContainers()) {
        context.log(`Container ${++containerCount}: ${container.name}`);
      }

      // Try to access the submissions container
      const containerClient = blobServiceClient.getContainerClient("submissions");

      // List some blobs in the container for debugging
      context.log("Listing first 10 blobs in submissions container:");
      let blobCount = 0;
      for await (const blob of containerClient.listBlobsFlat()) {
        if (blobCount++ < 10) {
          context.log(`Blob ${blobCount}: ${blob.name}`);
        } else {
          break;
        }
      }

      // If we have a zipBlobName, try to extract images directly from the zip file
      if (submission.zipBlobName) {
        context.log(`Attempting to extract images from zip file: ${submission.zipBlobName}`);
        try {
          const zipBlobClient = containerClient.getBlockBlobClient(submission.zipBlobName);
          const zipExists = await zipBlobClient.exists();

          if (zipExists) {
            context.log("Zip file exists, downloading...");
            const zipPath = path.join(tempDir, "submission.zip");
            await zipBlobClient.downloadToFile(zipPath);

            // We'll need to extract images from the zip file
            // This would require additional code to extract and process the zip
            // For now, we'll continue with the existing approach
            context.log("Zip file downloaded, but extraction not implemented yet");
          } else {
            context.warn(`Zip file ${submission.zipBlobName} not found`);
          }
        } catch (zipError: any) {
          context.warn(`Error accessing zip file: ${zipError.message}`);
        }
      }

      // Try to download images using the attachment URLs
      for (const [imageName, imageUrl] of Object.entries(submission.attachmentUrls || {})) {
        try {
          context.log(`Processing image: ${imageName} with URL: ${imageUrl}`);

          // Try different approaches to get the image

          // Approach 1: Direct blob access using the image name
          try {
            const blobPath = imageName;
            const blockBlobClient = containerClient.getBlockBlobClient(blobPath);

            const exists = await blockBlobClient.exists();
            if (exists) {
              context.log(`Image blob exists at path: ${blobPath}`);
              // Create a simplified filename without the path structure
              const simplifiedName = path.basename(imageName);
              const imagePath = path.join(tempDir, simplifiedName);

              // Download the file
              await blockBlobClient.downloadToFile(imagePath);

              // Store both the full path and the simplified name in the map
              // This is important because the payload references the simplified name
              imageMap[imageName] = imagePath;
              imageMap[simplifiedName] = imagePath;

              context.log(`Successfully downloaded image: ${imageName} to ${imagePath}`);
              continue; // Skip to next image if successful
            } else {
              context.log(`Image blob not found at path: ${blobPath}`);
            }
          } catch (approach1Error: any) {
            context.log(`Approach 1 failed: ${approach1Error.message}`);
          }

          // Approach 2: Try to extract blob path from URL
          try {
            const url = imageUrl as string;
            // Extract the blob path from the URL
            const urlObj = new URL(url);
            const pathParts = urlObj.pathname.split('/');
            // Remove empty parts and the container name
            const blobPath = pathParts.filter(p => p).slice(1).join('/');

            context.log(`Extracted blob path from URL: ${blobPath}`);

            if (blobPath) {
              const blockBlobClient = containerClient.getBlockBlobClient(blobPath);
              const exists = await blockBlobClient.exists();

              if (exists) {
                context.log(`Image blob exists at extracted path: ${blobPath}`);
                // Create a simplified filename without the path structure
                const simplifiedName = path.basename(imageName);
                const imagePath = path.join(tempDir, simplifiedName);

                // Download the file
                await blockBlobClient.downloadToFile(imagePath);

                // Store both the full path and the simplified name in the map
                imageMap[imageName] = imagePath;
                imageMap[simplifiedName] = imagePath;

                context.log(`Successfully downloaded image: ${imageName} to ${imagePath}`);
                continue; // Skip to next image if successful
              } else {
                context.log(`Image blob not found at extracted path: ${blobPath}`);
              }
            }
          } catch (approach2Error: any) {
            context.log(`Approach 2 failed: ${approach2Error.message}`);
          }

          // Approach 3: Try to use the submission ID and image name
          try {
            const blobPath = `${submission.id}/${imageName}`;
            context.log(`Trying path with submission ID: ${blobPath}`);

            const blockBlobClient = containerClient.getBlockBlobClient(blobPath);
            const exists = await blockBlobClient.exists();

            if (exists) {
              context.log(`Image blob exists at path with submission ID: ${blobPath}`);
              // Create a simplified filename without the path structure
              const simplifiedName = path.basename(imageName);
              const imagePath = path.join(tempDir, simplifiedName);

              // Download the file
              await blockBlobClient.downloadToFile(imagePath);

              // Store both the full path and the simplified name in the map
              imageMap[imageName] = imagePath;
              imageMap[simplifiedName] = imagePath;

              context.log(`Successfully downloaded image: ${imageName} to ${imagePath}`);
              continue; // Skip to next image if successful
            } else {
              context.log(`Image blob not found at path with submission ID: ${blobPath}`);
            }
          } catch (approach3Error: any) {
            context.log(`Approach 3 failed: ${approach3Error.message}`);
          }

          context.warn(`All approaches failed for image: ${imageName}`);
        } catch (imgError: any) {
          context.warn(`Failed to process image ${imageName}: ${imgError.message}`);
        }
      }

      context.log(`Downloaded ${Object.keys(imageMap).length} images out of ${Object.keys(submission.attachmentUrls || {}).length} URLs`);
    } catch (error: any) {
      context.error(`Error downloading images: ${error.message}`, {
        stack: error.stack,
        submissionId: submission.id
      });
      // Continue even if image downloads fail
    }

    // 4) Generate HTML for the document with embedded base64 images
    const html = await generateDocumentHtml(submission, imageMap, context);

    // 5) Convert HTML to DOCX using a simplified approach
    // Create a very basic HTML structure with minimal styling
    const simplifiedHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>Machine Guarding Assessment Report</title>
          <style>
            body {
              font-family: Calibri, Arial, sans-serif;
              font-size: 11pt;
              line-height: 1.5;
              color: #333333;
            }
            h1 {
              font-family: Calibri, Arial, sans-serif;
              color: #1a73e8;
              font-size: 24pt;
            }
            h2 {
              font-family: Calibri, Arial, sans-serif;
              color: #1a73e8;
              font-size: 18pt;
            }
            h3 {
              font-family: Calibri, Arial, sans-serif;
              color: #1a73e8;
              font-size: 14pt;
            }
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 15px 0;
            }
            th {
              background-color: #1a73e8;
              color: white;
              padding: 8px;
              text-align: left;
              font-family: Calibri, Arial, sans-serif;
            }
            td {
              padding: 8px;
              border: 1px solid #ddd;
              font-family: Calibri, Arial, sans-serif;
            }
            img {
              max-width: 500px;
              max-height: 350px;
            }
            .page-break {
              page-break-before: always;
            }
          </style>
        </head>
        <body>
          ${html}
        </body>
      </html>
    `;

    // Use minimal options for the conversion
    const buffer = await htmlToDocx(simplifiedHtml);

    // 6) Authenticate to Microsoft Graph and upload
    const credential = new ClientSecretCredential(
      process.env.AZURE_TENANT_ID!,
      process.env.AZURE_CLIENT_ID!,
      process.env.AZURE_CLIENT_SECRET!
    );
    const graph = Client.initWithMiddleware({
      authProvider: {
        getAccessToken: async () =>
          (await credential.getToken(".default"))?.token || "",
      },
    });

    // 7) Pick a non‑conflicting filename
    const siteId = process.env.SHAREPOINT_SITE_ID!;
    let attempt = 0;
    let uploadPath: string;
    while (true) {
      const suffix = attempt === 0 ? "" : `-${attempt}`;
      uploadPath = `Summaries/${submissionId}${suffix}-html.docx`;
      try {
        await graph.api(`/sites/${siteId}/drive/root:/${uploadPath}`).get();
        attempt++;
      } catch (e: any) {
        if (e.statusCode === 404) break;
        throw e;
      }
    }

    // 8) Final upload with proper content type
    try {
      await graph
        .api(`/sites/${siteId}/drive/root:/${uploadPath}:/content`)
        .header("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
        .put(buffer);

      context.log(`✔️ Uploaded submission doc as ${uploadPath}`);
    } catch (uploadError: any) {
      context.error(`Error uploading document: ${uploadError.message}`);
      throw uploadError;
    }
  } catch (error: any) {
    context.error("Function failed", {
      error: error.message,
      stack: error.stack,
      functionName: "processSummaryHtml"
    });
    throw error; // Rethrow to trigger retry
  } finally {
    // Clean up temporary directory
    try {
      fs.rmSync(tempDir, { recursive: true, force: true });
      context.log(`Cleaned up temporary directory: ${tempDir}`);
    } catch (cleanupError: any) {
      context.warn(`Failed to clean up temporary directory: ${cleanupError.message}`);
    }
  }
};

/**
 * Generates HTML for the document with embedded images
 */
async function generateDocumentHtml(
  submission: SubmissionData,
  _imageMap: Record<string, string>,
  _context: InvocationContext
): Promise<string> {
  const { payload } = submission;
  const machines = payload.section_2 || [];

  // Simple function to convert image to base64
  const getImageAsBase64 = (imageName: string): string => {
    try {
      // Try to get the image path using the full name
      if (_imageMap[imageName]) {
        const imagePath = _imageMap[imageName];

        // Check if file exists
        if (!fs.existsSync(imagePath)) {
          return '';
        }

        const imageBuffer = fs.readFileSync(imagePath);
        const extension = path.extname(imageName).toLowerCase().substring(1);
        const mimeType = extension === 'png' ? 'image/png' :
                         extension === 'gif' ? 'image/gif' : 'image/jpeg';

        return `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
      }

      // If not found, try with just the basename
      const baseName = path.basename(imageName);
      if (_imageMap[baseName]) {
        const imagePath = _imageMap[baseName];

        // Check if file exists
        if (!fs.existsSync(imagePath)) {
          return '';
        }

        const imageBuffer = fs.readFileSync(imagePath);
        const extension = path.extname(baseName).toLowerCase().substring(1);
        const mimeType = extension === 'png' ? 'image/png' :
                         extension === 'gif' ? 'image/gif' : 'image/jpeg';

        return `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
      }
    } catch (error) {
      // Silently fail and return empty string
    }
    return ''; // Return empty string if image not found or error
  };

  // Create an extremely basic HTML document with minimal styling
  let html = `<!DOCTYPE html>
<html>
<body>
<h1>Machine Guarding Assessment Report</h1>
<p>${payload.formName || "Odiz Machine Guarding Assessment"}</p>

<p><strong>Facility:</strong> ${payload.main_form_facility_name || ""}</p>
<p><strong>Assessment Date:</strong> ${formatDate(payload.main_form_date)}</p>
<p><strong>Assessor:</strong> ${payload.main_form_first_name || ""} ${payload.main_form_last_name || ""}</p>
<p><strong>Contact:</strong> ${payload.main_form_email_address || ""} ${payload.main_form_phone_number ? `| ${payload.main_form_phone_number}` : ""}</p>
<p><strong>Submission ID:</strong> ${submission.id}</p>
<p><strong>Report Generated:</strong> ${new Date().toLocaleDateString()}</p>

<br style="page-break-before: always">

<h1>Assessment Summary</h1>
<p>This report provides a detailed assessment of machine guarding and safety measures for the equipment evaluated.
Each machine has been assessed for compliance with applicable safety standards and recommendations for
improvements have been provided where necessary.</p>

<p>Machine guarding is a critical component of workplace safety, designed to protect operators and other employees
from hazards created by moving machine parts, flying debris, and other potential dangers. This assessment
identifies potential hazards and recommends appropriate guarding solutions to mitigate risks and ensure
compliance with safety standards.</p>

<h2>Assessment Details</h2>
<table border="1" cellspacing="0" cellpadding="5">
<tr>
  <td><strong>Facility Name</strong></td>
  <td>${payload.main_form_facility_name || ""}</td>
</tr>
<tr>
  <td><strong>Assessment Date</strong></td>
  <td>${formatDate(payload.main_form_date)}</td>
</tr>
<tr>
  <td><strong>Assessor</strong></td>
  <td>${payload.main_form_first_name || ""} ${payload.main_form_last_name || ""}</td>
</tr>
<tr>
  <td><strong>Contact Information</strong></td>
  <td>${payload.main_form_email_address || ""} ${payload.main_form_phone_number ? `| ${payload.main_form_phone_number}` : ""}</td>
</tr>
<tr>
  <td><strong>Number of Machines</strong></td>
  <td>${machines.length}</td>
</tr>
</table>

<br style="page-break-before: always">

<h1>Machines Assessed</h1>
<p>The following machines were assessed as part of this evaluation.</p>
`;

  // Add a simple list of machines
  if (machines.length > 0) {
    html += `<ul>`;
    for (let i = 0; i < machines.length; i++) {
      const machine = machines[i];
      const machineTitle = machine.fields.main_form_machine_title || `Machine ${i + 1}`;
      html += `<li>${machineTitle}</li>`;
    }
    html += `</ul>`;
  }

  // Add machines table
  if (machines.length > 0) {
    html += `
<table border="1" cellspacing="0" cellpadding="5">
<tr>
  <td><strong>Machine Name</strong></td>
  <td><strong>Machine Type</strong></td>
  <td><strong>Location</strong></td>
  <td><strong>Department</strong></td>
</tr>
    `;

    for (let i = 0; i < machines.length; i++) {
      const machine = machines[i];
      const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};

      html += `
<tr>
  <td>${machine.fields.main_form_machine_title || "Unnamed Machine"}</td>
  <td>${formatMachineType(machineSubform.general_machine_type)}</td>
  <td>${machineSubform.location_of_machine || ""}</td>
  <td>${machineSubform.department || ""}</td>
</tr>
      `;
    }

    html += `</table>`;
  }

  html += `<br style="page-break-before: always">`;

  // Process each machine
  for (let i = 0; i < machines.length; i++) {
    const machine = machines[i];
    const machineSubform = machine.fields.main_form_general_machine_subform?.[0] || {};
    const machineTitle = machine.fields.main_form_machine_title || `Machine ${i + 1}`;

    html += `
<h2>${machineTitle}</h2>

<h3>Machine Overview</h3>
<table border="1" cellspacing="0" cellpadding="5">
<tr>
  <td><strong>Type</strong></td>
  <td>${formatMachineType(machineSubform.general_machine_type)}</td>
</tr>
<tr>
  <td><strong>ID</strong></td>
  <td>${machineSubform.machine_name_and_id || "Not specified"}</td>
</tr>
<tr>
  <td><strong>Location</strong></td>
  <td>${machineSubform.location_of_machine || "Not specified"}</td>
</tr>
<tr>
  <td><strong>Department</strong></td>
  <td>${machineSubform.department || "Not specified"}</td>
</tr>
</table>

<h3>Assessment Information</h3>
<table border="1" cellspacing="0" cellpadding="5">
<tr>
  <td><strong>Assessment Level</strong></td>
  <td>${formatAssessmentLevel(machineSubform.assessment_level)}</td>
</tr>
<tr>
  <td><strong>Machine in Operation</strong></td>
  <td>${formatYesNo(machineSubform.is_machine_in_operation)}</td>
</tr>
<tr>
  <td><strong>Machine Observed</strong></td>
  <td>${formatYesNo(machineSubform.was_machine_observed_during_observation)}</td>
</tr>
<tr>
  <td><strong>Known Injuries</strong></td>
  <td>${formatYesNo(machineSubform.known_injuries_associated_with_machine)}</td>
</tr>
</table>

<h3>Machine Specifications</h3>
<table border="1" cellspacing="0" cellpadding="5">
<tr>
  <td><strong>Manufacturer</strong></td>
  <td>${machineSubform.machine_manufacturer || "Not specified"}</td>
</tr>
<tr>
  <td><strong>Model Number</strong></td>
  <td>${machineSubform.machine_model_number || "Not specified"}</td>
</tr>
<tr>
  <td><strong>Serial Number</strong></td>
  <td>${machineSubform.machine_serial_number || "Not specified"}</td>
</tr>
<tr>
  <td><strong>Operator Names</strong></td>
  <td>${machineSubform.operator_names || "Not specified"}</td>
</tr>
</table>

${machineSubform.machine_description ? `
<h3>Machine Description</h3>
<p>${machineSubform.machine_description}</p>
` : ''}
    `;

    // Add machine photos
    const photoSections = [
      { title: "Machine Nameplate", photos: machineSubform.photo_of_machine_nameplate },
      { title: "Machine ID", photos: machineSubform.photo_of_machine_id_num },
      { title: "Machine Front View", photos: machineSubform.photo_of_machine_front },
      { title: "Machine Right View", photos: machineSubform.photo_of_machine_right },
      { title: "Machine Back View", photos: machineSubform.photo_of_machine_back },
      { title: "Machine Left View", photos: machineSubform.photo_of_machine_left }
    ];

    if (photoSections.some(section => section.photos && section.photos.length > 0)) {
      html += `<h3>Machine Photos</h3>`;

      for (const section of photoSections) {
        if (section.photos && section.photos.length > 0) {
          for (const photoData of section.photos) {
            const base64Image = getImageAsBase64(photoData.photo);
            if (base64Image) {
              html += `
<p><strong>${section.title}</strong></p>
<img src="${base64Image}" alt="${section.title}" width="400" />
<p>${photoData.comment || ""}</p>
              `;
            }
          }
        }
      }
    }

    // Add hazards and recommendations
    const hazards = machineSubform.section_40 || [];
    if (hazards.length > 0) {
      html += `<h3>Hazards and Recommendations</h3>`;

      for (let j = 0; j < hazards.length; j++) {
        const hazard = hazards[j];
        const hazardFields = hazard.fields || {};

        html += `
<h4>Hazard ${j + 1}: ${hazardFields.field_403 || "Unnamed Hazard"}</h4>
<p><strong>Description:</strong> ${hazardFields.field_405 || "No description provided"}</p>
        `;

        // Add hazard photos
        if (hazardFields.field_404 && hazardFields.field_404.length > 0) {
          for (const photoData of hazardFields.field_404) {
            const base64Image = getImageAsBase64(photoData.photo);
            if (base64Image) {
              html += `
<p><strong>Hazard Photo</strong></p>
<img src="${base64Image}" alt="Hazard Photo" width="400" />
<p>${photoData.comment || ""}</p>
              `;
            }
          }
        }

        if (hazardFields.field_406 && hazardFields.field_406.length > 0) {
          html += `<p><strong>Recommended Solutions:</strong></p>
<table border="1" cellspacing="0" cellpadding="5">`;

          for (const solution of hazardFields.field_406) {
            html += `<tr><td>${solution}</td></tr>`;
          }

          html += `</table>`;
        }

        if (hazardFields.multiline_5) {
          html += `<p><strong>Additional Notes:</strong> ${hazardFields.multiline_5}</p>`;
        }
      }
    }

    // Add page break after each machine except the last one
    if (i < machines.length - 1) {
      html += `<br style="page-break-before: always">`;
    }
  }

  html += `
<p>© ${new Date().getFullYear()} Odiz Assessment | Report generated on ${new Date().toLocaleDateString()}</p>
</body>
</html>
  `;

  return html;
}

// Helper functions
function formatDate(dateString?: string): string {
  if (!dateString) return "";
  try {
    return new Date(dateString).toLocaleDateString();
  } catch (e) {
    return dateString;
  }
}

function formatMachineType(types?: string[]): string {
  if (!types || types.length === 0) return "Unknown";

  const typeMap: Record<string, string> = {
    "general": "General Machine",
    "complex": "Complex Machine System",
    "abb_wheel": "Abrasive Wheel Grinder / Bench or Pedestal Stand",
    "band_saw": "Bandsaw",
    "cnc": "CNC Machine",
    "drill_press": "Drill Press",
    "honing": "Honing Machine",
    "boring": "Horizontal Boring Machine",
    "horizontal_lathe": "Horizontal Lathe",
    "hyd_press": "Hydraulic Press",
    "ironworker": "Ironworker",
    "milling_machine": "Manual Mill",
    "mech_press": "Mechanical Press",
    "pipe_bender": "Pipe Bender",
    "pipe_thread": "Pipe Threading Machine",
    "press_brake": "Press Break",
    "shear": "Shear",
    "surf_grinder": "Surface Grinder",
    "vtl": "Vertical Turret Lathe",
    "other": "Other"
  };

  return types.map(type => typeMap[type] || type).join(", ");
}

function formatAssessmentLevel(levels?: string[]): string {
  if (!levels || levels.length === 0) return "Unknown";

  const levelMap: Record<string, string> = {
    "osha": "OSHA Standards",
    "ansi-iso": "ANSI/ISO Standards",
    "none": "No Specific Standard"
  };

  return levels.map(level => levelMap[level] || level).join(", ");
}

function formatYesNo(value?: number): string {
  if (value === undefined || value === null) return "Unknown";
  return value === 0 ? "Yes" : value === 1 ? "No" : "N/A";
}

app.storageQueue("processSummaryHtml", {
  queueName: "summary-tasks-html",
  connection: "AzureWebJobsStorage",
  handler: processSummaryHtml,
});
