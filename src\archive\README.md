# Archived Files

This directory contains files that are no longer in use but are kept for reference.

## processSummaryHtml.ts

This file was archived on May 23, 2025 because:

- The HTML-based document generation approach was replaced by the DOCX-based approach in `processSummaryDocx.ts`
- The DOCX-based approach provides better formatting control and more professional output
- Maintaining two separate document generation processes was redundant

The functionality has been consolidated into the `processSummaryDocx.ts` file, which now handles all document generation needs.

## processSummary.ts

This file was archived on May 23, 2025 because:

- It was an earlier version of the document generation process that used a different approach
- The function was registered to listen to the `summary-tasks` queue, which is no longer being used
- The newer `processSummaryDocx.ts` function provides enhanced formatting and more comprehensive reports
- The function was still deployed but dormant as no messages were being sent to its queue

The functionality has been replaced by the improved `processSummaryDocx.ts` implementation.

## testFunction.ts

This file was archived on May 23, 2025 because:

- It was a test/development function that is no longer needed in production
- The core functionality of the application is now stable and well-tested
- Removing unnecessary endpoints improves security and reduces maintenance overhead

This was a simple HTTP-triggered function used during development for testing purposes.
