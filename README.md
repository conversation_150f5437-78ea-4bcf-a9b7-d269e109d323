# FastField Integration

This project contains Azure Functions for processing FastField form submissions.

## Architecture

The application uses a multi-function approach to handle large ZIP file uploads containing JSON data and images:

### Function Flow

1. **receiveZipFile** (HTTP Trigger)
   - Receives the ZIP file via HTTP request
   - Stores the ZIP file in blob storage
   - Queues a message for processing
   - Returns a 202 Accepted response

2. **processZipFile** (Queue Trigger)
   - Triggered by messages in the `zip-processing` queue
   - Downloads the ZIP file from blob storage
   - Extracts and processes the JSON data
   - Inserts the JSON data into SQL
   - Queues individual image processing tasks
   - Queues a summary processing task

3. **processImage** (Queue Trigger)
   - Triggered by messages in the `image-processing` queue
   - Processes a single image from the ZIP file
   - Uploads the image to blob storage
   - Updates the database with the image URL

4. **processSummary** (Queue Trigger)
   - Triggered by messages in the `summary-tasks` queue
   - Generates a summary document from the submission data
   - Uploads the document to SharePoint

### Benefits of Multi-Function Approach

- **Avoids Timeouts**: Each function performs a specific task that completes quickly
- **Parallel Processing**: Multiple images can be processed simultaneously
- **Resilience**: If any step fails, only that part needs to be retried
- **Scalability**: Functions can scale independently based on load

## Database Schema

The `FastFieldSubmissions` table has the following structure:

- `Id` (int, primary key)
- `Payload` (nvarchar(max))
- `AttachmentUrls` (nvarchar(max))
- `ZipBlobName` (nvarchar(255))

## Storage

- **Blob Containers**:
  - `zip-submissions`: Stores the original ZIP files
  - `submissions`: Stores the extracted images

- **Queues**:
  - `zip-processing`: Triggers ZIP file processing
  - `image-processing`: Triggers image processing
  - `summary-tasks`: Triggers summary document generation

## Environment Variables

- `SQL_CONN_STR`: SQL Server connection string
- `AzureWebJobsStorage`: Azure Storage connection string
- `FASTFIELD_DEV_API_KEY`: API key for FastField authentication
- `AZURE_TENANT_ID`, `AZURE_CLIENT_ID`, `AZURE_CLIENT_SECRET`: For Microsoft Graph authentication
- `SHAREPOINT_SITE_ID`: SharePoint site ID for document upload

## Development

1. Install dependencies:
   ```
   npm install
   ```

2. Run locally:
   ```
   npm start
   ```

3. Deploy to Azure:
   ```
   func azure functionapp publish <app-name>
   ```
